import os
import time
from typing import TypedDict, Annotated, List
from langchain_core.messages import BaseMessage, AnyMessage, HumanMessage, ToolMessage, AIMessage, SystemMessage
from langchain_ollama import ChatOllama
from langgraph.graph import StateGraph, END
from dotenv import load_dotenv
from langchain.tools import tool
from langchain_community.tools import DuckDuckGoSearchRun
from rag import get_retriever
from logging_config import setup_logging
from langchain.globals import set_debug

set_debug(True)
logger = setup_logging()
load_dotenv()

class AgentState(TypedDict):
    user_query: str
    messages: Annotated[List[AnyMessage], lambda x, y: x + y]
    code_analysis_result: str | None
    doc_search_result: str | None
    web_search_result: str | None
    generated_code: str | None
    generated_tests: str | None
    turn_count: int

@tool
def codebase_retriever(query: str) -> str:
    """Retrieves relevant code snippets from the local codebase based on a query."""
    retriever = get_retriever()
    results = retriever.invoke(query)
    return "\n---\n".join([f"File: {doc.metadata.get('source', 'N/A')}\n\n```python\n{doc.page_content}\n```" for doc in results])

@tool
def documentation_retriever(query: str) -> str:
    """Retrieves documentation based on a query."""
    return f"Placeholder documentation for query '{query}': Found API specs and usage examples."

@tool
def web_search(query: str) -> str:
    """
    Performs a web search using DuckDuckGo for external libraries, concepts, or errors.
    """
    logger.info(f"Performing web search for query: '{query}'")
    search = DuckDuckGoSearchRun()
    try:
        result = search.run(query)
        formatted_result = f"Web Search Results for '{query}':\n{result}"
        logger.info(f"Web search for '{query}' returned {len(result)} characters.")
        return formatted_result
    except Exception as e:
        logger.error(f"Web search for '{query}' failed: {e}", exc_info=True)
        return "Web search failed due to an error."

# Map tool names to their functions for dynamic execution
tool_map = {
    "web_search": web_search,
    "codebase_retriever": codebase_retriever,
    "documentation_retriever": documentation_retriever,
}

ollama_client1 = ChatOllama(
    base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
    model=os.getenv("OLLAMA_MODEL_1"),
    temperature=0.1,
)
ollama_client2 = ChatOllama(
    base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
    model=os.getenv("OLLAMA_MODEL_2"),
    temperature=0.1,
)

tools = [codebase_retriever, documentation_retriever, web_search]
lead_researcher_llm = ollama_client2.bind_tools(tools)
code_generator_llm = ollama_client2
test_generator_llm = ollama_client2

def lead_researcher_node(state: AgentState):
    """The lead researcher node that orchestrates the research process."""
    if "turn_count" not in state:
        state["turn_count"] = 0
    state["turn_count"] += 1
    logger.info(f"Lead researcher turn {state['turn_count']}. Invoking LLM...")

    # A more robust system prompt that encourages planning.
    system_prompt = """You are a lead research agent. Your goal is to answer the user's query by orchestrating a team of specialist agents.

1.  **Analyze**: First, analyze the user's query and the conversation history.
2.  **Review**: Second, review the information already gathered in the 'code_analysis_result', 'doc_search_result', and 'web_search_result' fields.
3.  **Plan**: Third, form a step-by-step plan for what new information is needed.
4.  **Execute**: Fourth, decide on the next set of tool calls. Crucially, do NOT call a tool if the required information is already present in the results you reviewed.

The available tools are:
- `codebase_retriever`: Use this to search for code snippets within the local project codebase.
- `documentation_retriever`: Use this to search for official documentation related to the query.
- `web_search`: Use this for general web searches about external libraries, errors, or concepts.

If you have gathered enough information, you can stop calling tools.
Respond *only* with the new, unique tool calls to make. Do not add any other text.
"""
    
    messages_to_send = [SystemMessage(content=system_prompt)] + state['messages']
    # Always remind the agent of the original goal.
    messages_to_send.append(HumanMessage(content=f"My original query was: '{state['user_query']}'. Based on your plan and the conversation so far, what is the next single tool call to make?"))
    
    try:
        response = lead_researcher_llm.invoke(messages_to_send)
        logger.info("Lead researcher LLM invocation complete.")
    except Exception as e:
        logger.error(f"LLM invocation failed: {e}", exc_info=True)
        response = AIMessage(content="LLM_INVOCATION_FAILED")

    return {"messages": [response], "turn_count": state["turn_count"]}

def tool_executor_node(state: AgentState):
    """Executes multiple tool calls in parallel and collects results."""
    logger.info("Entering tool executor node.")
    new_messages = []
    updates = {}
    
    # Ensure tool_calls exist and are not empty
    if not state["messages"][-1].tool_calls:
        logger.warning("No tool calls found in the last message.")
        return {"messages": new_messages}

    tool_calls = state["messages"][-1].tool_calls
    unique_calls = []
    seen = set()
    for call in tool_calls:
        # Create a unique, hashable representation of the call
        # Convert args to a frozenset of (key, value) pairs for hashing
        call_signature = (call['name'], frozenset(call.get('args', {}).items()))
        if call_signature not in seen:
            unique_calls.append(call)
            seen.add(call_signature)

    for tool_call in unique_calls:
        tool_name = tool_call.get("name")
        tool_args = tool_call.get("args", {})
        tool_id = tool_call.get("id")

        if tool_name in tool_map:
            try:
                logger.info(f"Executing unique tool: {tool_name} with args: {tool_args}")
                tool_result = tool_map[tool_name].invoke(tool_args)
                tool_message = ToolMessage(content=tool_result, tool_call_id=tool_id)
                new_messages.append(tool_message)

                # Aggregate results into the state, appending to existing content
                if tool_name == "web_search":
                    if updates.get("web_search_result"):
                        updates["web_search_result"] += f"\n---\n{tool_result}"
                    else:
                        updates["web_search_result"] = tool_result
                elif tool_name == "codebase_retriever":
                    if updates.get("code_analysis_result"):
                        updates["code_analysis_result"] += f"\n---\n{tool_result}"
                    else:
                        updates["code_analysis_result"] = tool_result
                elif tool_name == "documentation_retriever":
                    if updates.get("doc_search_result"):
                        updates["doc_search_result"] += f"\n---\n{tool_result}"
                    else:
                        updates["doc_search_result"] = tool_result
            except Exception as e:
                logger.error(f"Error executing tool {tool_name}: {e}", exc_info=True)
                error_message = ToolMessage(content=f"Error executing {tool_name}: {e}", tool_call_id=tool_id)
                new_messages.append(error_message)
        else:
            logger.warning(f"Unknown tool: {tool_name}")
            error_message = ToolMessage(content=f"Unknown tool: {tool_name}", tool_call_id=tool_id)
            new_messages.append(error_message)
    
    updates["messages"] = new_messages
    return updates

# Add this new node function
def context_summarizer_node(state: AgentState):
    """Summarizes the results of the last tool execution for the planner."""
    logger.info("Entering context summarizer node.")

    # 1. Find the last AI message to determine how many tools were called.
    last_ai_message = None
    for msg in reversed(state['messages']):
        if isinstance(msg, AIMessage) and msg.tool_calls:
            last_ai_message = msg
            break
    
    # If for some reason we can't find a relevant AI message, pass through.
    if not last_ai_message:
        logger.warning("Summarizer could not find an AIMessage with tool_calls to summarize.")
        return {}

    num_tool_calls = len(last_ai_message.tool_calls)
    
    # 2. The tool messages to summarize are the last N messages in the list.
    # These will be the ToolMessage objects from the previous step.
    tool_messages_to_summarize = state['messages'][-num_tool_calls:]
    tool_outputs_for_summary = "\n---\n".join([str(msg.content) for msg in tool_messages_to_summarize])

    if not tool_outputs_for_summary.strip():
        summary_content = "No new information was gathered from the tools."
    else:
        # 3. Summarize the tool outputs using an LLM.
        prompt = f"""You are a helpful assistant. Briefly summarize the following tool results in one or two sentences for a planning agent.

        **Tool Results:**
        {tool_outputs_for_summary[:4000]}

        **Summary:**
        """
        try:
            response = code_generator_llm.invoke([HumanMessage(content=prompt)])
            summary_content = response.content
        except Exception as e:
            logger.error(f"Summarization LLM failed: {e}")
            summary_content = "Failed to summarize tool results."

    # 4. Replace the verbose tool results with a single summary message.
    # The tool_call_id is arbitrary here, as it's just for context.
    summary_message = ToolMessage(content=summary_content, tool_call_id="summary_tool_call")
    
    # Rebuild the history by removing the last N tool messages and adding the summary.
    messages_without_last_tools = state['messages'][:-num_tool_calls]
    messages_without_last_tools.append(summary_message)
    
    return {"messages": messages_without_last_tools}

def code_generator_node(state: AgentState):
    logger.info("Entering code generator node.")
    prompt = f"""
You are an expert programmer tasked with writing a complete, runnable Python script.
Synthesize the information from the context below to fulfill the user's request.
Base the code *only* on the provided context. Do not add functionality not present in the snippets.
Output only the raw Python code, with no commentary or explanation.

**User's Request:**
{state.get('user_query')}

**Relevant Code Snippets Found:**
{state.get('code_analysis_result') or 'No relevant code found.'}

**Relevant Documentation Found:**
{state.get('doc_search_result') or 'No relevant documentation found.'}

**Relevant Web Search Results Found:**
{state.get('web_search_result') or 'No relevant web search results found.'}
"""
    response = code_generator_llm.invoke([HumanMessage(content=prompt)])
    logger.info("Code generator LLM invocation complete.")
    return {"generated_code": response.content}

def test_generator_node(state: AgentState):
    response = test_generator_llm.invoke([HumanMessage(content=f"Generate tests for:\n{state['generated_code']}")])
    return {"generated_tests": response.content}

def route_logic(state: AgentState) -> str:
    # FIRST: Check for a critical failure and end the workflow if found.
    if state["messages"][-1].content == "LLM_INVOCATION_FAILED":
        logger.error("Lead researcher failed to get a response. Terminating workflow.")
        return END

    # If no failure, proceed with the normal logic.
    if state.get("turn_count", 0) > 4:
        logger.warning("Turn count limit reached. Forcing code generation.")
        return "code_generator_node"

    if not state["messages"][-1].tool_calls:
        logger.info("No tool calls detected. Routing to code_generator_node.")
        return "code_generator_node"

    logger.info("Tool calls detected. Routing to tool_executor_node.")
    return "tool_executor_node"

workflow = StateGraph(AgentState)
workflow.add_node("lead_researcher_node", lead_researcher_node)
workflow.add_node("tool_executor_node", tool_executor_node)
workflow.add_node("context_summarizer_node", context_summarizer_node) # Add the new node
workflow.add_node("code_generator_node", code_generator_node)
workflow.add_node("test_generator_node", test_generator_node)

workflow.set_entry_point("lead_researcher_node")

workflow.add_conditional_edges(
    "lead_researcher_node",
    route_logic,
    {
        "tool_executor_node": "tool_executor_node",
        "code_generator_node": "code_generator_node",
        # Add the new explicit end path for failures.
        END: END
    }
)

# Update the edges
workflow.add_edge("tool_executor_node", "context_summarizer_node")
workflow.add_edge("context_summarizer_node", "lead_researcher_node")

workflow.add_edge("code_generator_node", "test_generator_node")
workflow.add_edge("test_generator_node", END)
compiled_app = workflow.compile()

Collecting vllm==0.8.5
  Using cached vllm-0.8.5-cp38-abi3-manylinux1_x86_64.whl.metadata (14 kB)
Requirement already satisfied: gguf in /home/<USER>/miniconda3/lib/python3.11/site-packages (0.10.0)
Requirement already satisfied: tokenizers in /home/<USER>/miniconda3/lib/python3.11/site-packages (0.20.3)
Requirement already satisfied: torch==2.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (2.6.0)
Requirement already satisfied: transformers in /home/<USER>/miniconda3/lib/python3.11/site-packages (4.46.3)
Requirement already satisfied: cachetools in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (5.5.0)
Requirement already satisfied: psutil in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (5.9.5)
Requirement already satisfied: sentencepiece in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.2.0)
Requirement already satisfied: numpy in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.4)
Requirement already satisfied: requests>=2.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (2.32.3)
Requirement already satisfied: tqdm in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.67.1)
Requirement already satisfied: blake3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.0.4)
Requirement already satisfied: py-cpuinfo in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (9.0.0)
Collecting transformers
  Using cached transformers-4.52.4-py3-none-any.whl.metadata (38 kB)
Requirement already satisfied: huggingface-hub>=0.30.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from huggingface-hub[hf_xet]>=0.30.0->vllm==0.8.5) (0.30.2)
Collecting tokenizers
  Using cached tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Requirement already satisfied: protobuf in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.25.7)
Requirement already satisfied: fastapi>=0.115.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (0.115.6)
Requirement already satisfied: aiohttp in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.11.11)
Requirement already satisfied: openai>=1.52.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.58.1)
Requirement already satisfied: pydantic>=2.9 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (2.10.4)
Requirement already satisfied: prometheus_client>=0.18.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.21.1)
Requirement already satisfied: pillow in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (11.0.0)
Requirement already satisfied: prometheus-fastapi-instrumentator>=7.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (7.1.0)
Requirement already satisfied: tiktoken>=0.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.8.0)
Requirement already satisfied: lm-format-enforcer<0.11,>=0.10.11 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.10.11)
Requirement already satisfied: llguidance<0.8.0,>=0.7.9 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.7.19)
Requirement already satisfied: outlines==0.1.11 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.1.11)
Requirement already satisfied: lark==1.2.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.2.2)
Requirement already satisfied: xgrammar==0.1.18 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.1.18)
Requirement already satisfied: typing_extensions>=4.10 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.12.2)
Requirement already satisfied: filelock>=3.16.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.16.1)
Requirement already satisfied: partial-json-parser in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.2.1.1.post5)
Requirement already satisfied: pyzmq>=25.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (26.4.0)
Requirement already satisfied: msgspec in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.19.0)
Collecting gguf
  Using cached gguf-0.17.0-py3-none-any.whl.metadata (4.4 kB)
Requirement already satisfied: importlib_metadata in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (8.0.0)
Requirement already satisfied: mistral_common>=1.5.4 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from mistral_common[opencv]>=1.5.4->vllm==0.8.5) (1.5.4)
Requirement already satisfied: opencv-python-headless>=4.11.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.11.0.86)
Requirement already satisfied: pyyaml in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (6.0.2)
Requirement already satisfied: einops in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.6.1)
Requirement already satisfied: compressed-tensors==0.9.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.9.3)
Requirement already satisfied: depyf==0.18.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.18.0)
Requirement already satisfied: cloudpickle in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.1.1)
Requirement already satisfied: watchfiles in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.0.3)
Requirement already satisfied: python-json-logger in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.3.0)
Requirement already satisfied: scipy in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.12.0)
Requirement already satisfied: ninja in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (********)
Requirement already satisfied: opentelemetry-sdk<1.27.0,>=1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-api<1.27.0,>=1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-exporter-otlp<1.27.0,>=1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-semantic-conventions-ai<0.5.0,>=0.4.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.4.3)
Requirement already satisfied: numba==0.61.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.61.2)
Requirement already satisfied: ray!=2.44.*,>=2.43.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (2.43.0)
Requirement already satisfied: torchaudio==2.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (2.6.0)
Requirement already satisfied: torchvision==0.21.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.21.0)
Requirement already satisfied: xformers==0.0.29.post2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.0.29.post2)
Requirement already satisfied: networkx in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (3.1)
Requirement already satisfied: jinja2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (3.1.4)
Requirement already satisfied: fsspec in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (2024.10.0)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (9.1.0.70)
Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.5.8)
Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (11.2.1.3)
Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (10.3.5.147)
Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (11.6.1.9)
Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.3.1.170)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (0.6.2)
Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (2.21.5)
Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: triton==3.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (3.2.0)
Requirement already satisfied: sympy==1.13.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (1.13.1)
Requirement already satisfied: astor in /home/<USER>/miniconda3/lib/python3.11/site-packages (from depyf==0.18.0->vllm==0.8.5) (0.8.1)
Requirement already satisfied: dill in /home/<USER>/miniconda3/lib/python3.11/site-packages (from depyf==0.18.0->vllm==0.8.5) (0.3.6)
Requirement already satisfied: llvmlite<0.45,>=0.44.0dev0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from numba==0.61.2->vllm==0.8.5) (0.44.0)
Requirement already satisfied: interegular in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (0.3.3)
Requirement already satisfied: nest_asyncio in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (1.6.0)
Requirement already satisfied: diskcache in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (5.6.3)
Requirement already satisfied: referencing in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (0.35.1)
Requirement already satisfied: jsonschema in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (4.23.0)
Requirement already satisfied: pycountry in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (23.12.11)
Requirement already satisfied: airportsdata in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (20250224)
Requirement already satisfied: outlines_core==0.1.26 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (0.1.26)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from sympy==1.13.1->torch==2.6.0) (1.3.0)
Requirement already satisfied: packaging in /home/<USER>/miniconda3/lib/python3.11/site-packages (from lm-format-enforcer<0.11,>=0.10.11->vllm==0.8.5) (24.2)
Requirement already satisfied: deprecated>=1.2.6 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-api<1.27.0,>=1.26.0->vllm==0.8.5) (1.2.15)
Requirement already satisfied: zipp>=0.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from importlib_metadata->vllm==0.8.5) (3.21.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-http==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: googleapis-common-protos~=1.52 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.66.0)
Requirement already satisfied: grpcio<2.0.0,>=1.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.68.1)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-proto==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-semantic-conventions==0.47b0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-sdk<1.27.0,>=1.26.0->vllm==0.8.5) (0.47b0)
Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (3.4.0)
Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (2.2.3)
Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (2024.12.14)
Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from transformers) (2024.11.6)
Requirement already satisfied: safetensors>=0.4.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from transformers) (0.5.3)
Requirement already satisfied: wrapt<2,>=1.10 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from deprecated>=1.2.6->opentelemetry-api<1.27.0,>=1.26.0->vllm==0.8.5) (1.17.0)
Requirement already satisfied: starlette<0.42.0,>=0.40.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.41.3)
Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from pydantic>=2.9->vllm==0.8.5) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from pydantic>=2.9->vllm==0.8.5) (2.27.2)
Requirement already satisfied: anyio<5,>=3.4.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from starlette<0.42.0,>=0.40.0->fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (4.7.0)
Requirement already satisfied: sniffio>=1.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.4.0->starlette<0.42.0,>=0.40.0->fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.3.1)
Requirement already satisfied: fastapi-cli>=0.0.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.0.7)
Requirement already satisfied: httpx>=0.23.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (0.27.2)
Requirement already satisfied: python-multipart>=0.0.7 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (0.0.20)
Requirement already satisfied: email-validator>=2.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (2.2.0)
Requirement already satisfied: uvicorn>=0.12.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.34.0)
Requirement already satisfied: dnspython>=2.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from email-validator>=2.0.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (2.7.0)
Requirement already satisfied: typer>=0.12.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.15.1)
Requirement already satisfied: rich-toolkit>=0.11.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.14.3)
Requirement already satisfied: httpcore==1.* in /home/<USER>/miniconda3/lib/python3.11/site-packages (from httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.14.0)
Collecting hf-xet>=0.1.4 (from huggingface-hub[hf_xet]>=0.30.0->vllm==0.8.5)
  Downloading hf_xet-1.1.4-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (879 bytes)
Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jinja2->torch==2.6.0) (3.0.2)
Requirement already satisfied: attrs>=22.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jsonschema->outlines==0.1.11->vllm==0.8.5) (24.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jsonschema->outlines==0.1.11->vllm==0.8.5) (2024.10.1)
Requirement already satisfied: rpds-py>=0.7.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jsonschema->outlines==0.1.11->vllm==0.8.5) (0.22.3)
Requirement already satisfied: distro<2,>=1.7.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from openai>=1.52.0->vllm==0.8.5) (1.9.0)
Requirement already satisfied: jiter<1,>=0.4.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from openai>=1.52.0->vllm==0.8.5) (0.8.2)
Requirement already satisfied: click>=7.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (8.1.7)
Requirement already satisfied: msgpack<2.0.0,>=1.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (1.0.7)
Requirement already satisfied: aiosignal in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (1.3.2)
Requirement already satisfied: frozenlist in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (1.5.0)
Requirement already satisfied: cupy-cuda12x in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (13.4.1)
Requirement already satisfied: rich>=13.7.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (13.9.4)
Requirement already satisfied: markdown-it-py>=2.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from rich>=13.7.1->rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from rich>=13.7.1->rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (2.18.0)
Requirement already satisfied: mdurl~=0.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich>=13.7.1->rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.1.2)
Requirement already satisfied: shellingham>=1.3.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from typer>=0.12.3->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.5.4)
Requirement already satisfied: httptools>=0.6.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.6.4)
Requirement already satisfied: python-dotenv>=0.13 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.0.1)
Requirement already satisfied: uvloop!=0.15.0,!=0.15.1,>=0.14.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.21.0)
Requirement already satisfied: websockets>=10.4 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (14.1)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (2.4.4)
Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (6.1.0)
Requirement already satisfied: propcache>=0.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (0.2.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (1.18.3)
Requirement already satisfied: fastrlock>=0.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from cupy-cuda12x->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (0.8.3)
Using cached vllm-0.8.5-cp38-abi3-manylinux1_x86_64.whl (326.4 MB)
Using cached gguf-0.17.0-py3-none-any.whl (95 kB)
Using cached tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)
Using cached transformers-4.52.4-py3-none-any.whl (10.5 MB)
Downloading hf_xet-1.1.4-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.1/3.1 MB 10.0 MB/s eta 0:00:00
Installing collected packages: hf-xet, gguf, tokenizers, transformers, vllm
  Attempting uninstall: gguf
    Found existing installation: gguf 0.10.0
    Uninstalling gguf-0.10.0:
      Successfully uninstalled gguf-0.10.0
  Attempting uninstall: tokenizers
    Found existing installation: tokenizers 0.20.3
    Uninstalling tokenizers-0.20.3:
      Successfully uninstalled tokenizers-0.20.3
  Attempting uninstall: transformers
    Found existing installation: transformers 4.46.3
    Uninstalling transformers-4.46.3:
      Successfully uninstalled transformers-4.46.3

Successfully installed gguf-0.17.0 hf-xet-1.1.4 tokenizers-0.21.1 transformers-4.52.4 vllm-0.8.5

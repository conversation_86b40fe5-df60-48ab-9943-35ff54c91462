from langchain_ollama import OllamaEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_text_splitters import RecursiveCharacterTextSplitter, Language
from dotenv import load_dotenv
import os

load_dotenv()

# Initialize embeddings with configured model
embeddings = OllamaEmbeddings(
    base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
    model=os.getenv("OLLAMA_EMBEDDING_MODEL")
)

# Configure code-aware text splitter for Python
python_splitter = RecursiveCharacterTextSplitter.from_language(
    language=Language.PYTHON,
    chunk_size=1000,
    chunk_overlap=200
)

# Initialize ChromaDB with configured path
vector_db = Chroma(
    persist_directory=os.getenv("CODE_DB_PATH"),
    embedding_function=embeddings
)

# Existing document processing and DB population logic
# ... [rest of original file content]

# logging_config.py
import logging
import sys

def setup_logging(level=logging.INFO):
    # Create a logger
    logger = logging.getLogger('DeepResearchApp')
    logger.setLevel(level)

    # Create a handler (console handler)
    ch = logging.StreamHandler(sys.stdout) # Output to stdout
    ch.setLevel(level)

    # Create a formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s')
    ch.setFormatter(formatter)

    # Add the handler to the logger
    if not logger.handlers: # Avoid adding multiple handlers if called multiple times
        logger.addHandler(ch)

    return logger

# Get a logger instance for immediate use if needed elsewhere
# Or scripts can call setup_logging() themselves.
# logger = setup_logging()

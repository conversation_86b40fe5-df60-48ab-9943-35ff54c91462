# Code Researcher System

A comprehensive research assistant system combining RAG (Retrieval-Augmented Generation) capabilities with automated agent workflows for code analysis and documentation.

## Features

- **Iterative Research Loop**: Self-correcting workflow that cycles between analysis and research.
- **RAG Pipeline**: Automated document processing and vector storage using ChromaDB.
- **Agent Workflows**: 
  - Lead Researcher (Orchestrates research)
  - Code Analyzer (Searches local codebase)
  - Documentation Searcher (Searches documentation)
  - Web Searcher (Performs web searches)
- **API Integration**: RESTful endpoints for interacting with research modules.
- **Modern Frontend**: React-based UI.

## Architecture Overview

The system uses an iterative research pattern:

```mermaid
graph TD
    A[Lead Researcher] -->|Decision| B{Needs Research?}
    B -->|Yes| C[Tool Router]
    B -->|No| D[Code Generator]
    C -->|Code Analysis| E[Code Analyzer]
    C -->|Docs Search| F[Doc Searcher]
    C -->|Web Search| G[Web Searcher]
    E --> A
    F --> A
    G --> A
    D --> H[Test Generator]
    H --> I[END]
```

Workflow Process

    Lead Researcher receives the user query and forms a plan.

    Determines if additional research is needed.

    Routes to the appropriate tool via the Tool Router to execute the next step in the plan.

    Tool execution results feed back to the Lead Researcher.

    Process repeats until the research plan is complete.

    Final code is generated based on the gathered context, and then tests are created.

Installation

    Clone the repository.

    Install dependencies:
    ```bash
    npm install
    pip install -r requirements.txt
    ```

Set up your environment variables:
```bash
cp .env.example .env
# Edit the .env file with your specific configuration
```

Start the development servers:
```bash
# Frontend (from the /frontend-vite directory)
npm run dev

# Backend
python main_api.py
```

Configuration

Set these environment variables in .env:
```ini
# Example
OLLAMA_HOST_1=*************
OLLAMA_PORT_1=11434
OLLAMA_MODEL_1=okamototk/deepseek-r1:8b
OLLAMA_MODEL_2=okamototk/deepseek-r1:8b
OLLAMA_EMBEDDING_MODEL=all-minilm:l6-v2
CODE_DB_PATH=./chroma_db
```
Usage

Access the system at http://localhost:5173 after starting both servers.
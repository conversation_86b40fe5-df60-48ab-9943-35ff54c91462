from fastapi import FastAP<PERSON>, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from agent_workflow import compiled_app
from langchain_core.messages import HumanMessage, BaseMessage
import json
import asyncio
from logging_config import setup_logging

logger = setup_logging()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Add your frontend URLs
    allow_methods=["GET", "POST"],
    allow_headers=["Content-Type"],
)

def _serialize_agent_state(data: dict) -> dict:
    """Helper function to make the AgentState dictionary JSON serializable."""
    if 'messages' in data and isinstance(data.get('messages'), list):
        serializable_messages = []
        for msg in data['messages']:
            if isinstance(msg, BaseMessage):
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_call_names = [call.get('name') for call in msg.tool_calls if call.get('name')]
                    serializable_messages.append(f"Tool Call: {', '.join(tool_call_names)}")
                elif hasattr(msg, 'content'):
                    serializable_messages.append(msg.content)
                else:
                    serializable_messages.append(str(msg))
            else:
                 serializable_messages.append(str(msg))
        data['messages'] = serializable_messages
    return data

async def event_stream(query: str):
    """Streams research events back to the client with proper serialization."""
    logger.info(f"Event stream initiated for query: '{query}'")
    try:
        async for event in compiled_app.astream_events(
            {"user_query": query, "messages": []},
            version="v1",
            config={"recursion_limit": 50}
        ):
            # 1. Initialize payload to None at the start of every loop.
            payload = None
            
            if event["event"] == "on_chain_stream":
                payload = event.get("data", {}).get("chunk")
            elif event["event"] == "on_chain_end":
                payload = event.get("data", {}).get("output")

            # 2. Check that a payload exists before processing it.
            if payload:
                if isinstance(payload, dict):
                    serializable_payload = _serialize_agent_state(payload.copy())
                    yield f"data: {json.dumps(serializable_payload)}\n\n"
                else:
                    yield f"data: {json.dumps({'response': str(payload)})}\n\n"

    except Exception as e:
        logger.error(f"FATAL: An exception occurred during astream_events: {e}", exc_info=True)
        error_message = {"error": "An internal server error occurred during stream processing."}
        yield f"data: {json.dumps(error_message)}\n\n"
    finally:
        logger.info(f"Event stream process fully completed for query: '{query}'")
        yield "data: [DONE]\n\n"

@app.get("/research-stream")
async def stream_research(query: str):
    """Endpoint to initiate and stream the research process via GET."""
    if not query:
        logger.warning("Request received with no query.")
        # We can simply return an empty stream or let the frontend handle it
        return StreamingResponse(iter(["data: [DONE]\n\n"]), media_type="text/event-stream")
    
    return StreamingResponse(event_stream(query), media_type="text/event-stream")

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting FastAPI server...")
    uvicorn.run(app, host="0.0.0.0", port=8003)

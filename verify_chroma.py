import os
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
import logging

load_dotenv()

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def verify_chroma_parsing():
    code_db_path = os.getenv("CODE_DB_PATH")
    if not code_db_path:
        logger.error("CODE_DB_PATH environment variable not set.")
        return

    logger.info(f"Attempting to load ChromaDB from: {code_db_path}")

    try:
        embedding_function = OllamaEmbeddings(
            base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
            model=os.getenv("OLLAMA_EMBEDDING_MODEL")
        )
        
        # Load the existing ChromaDB
        db = Chroma(
            persist_directory=code_db_path,
            embedding_function=embedding_function
        )
        logger.info("ChromaDB loaded successfully.")

        # Perform a similarity search
        query = "rag.py file content"
        logger.info(f"Performing similarity search for query: '{query}'")
        results = db.similarity_search(query, k=5) # Retrieve top 5 results

        if results:
            logger.info(f"Found {len(results)} relevant documents for the query.")
            for i, doc in enumerate(results):
                logger.info(f"--- Document {i+1} ---")
                logger.info(f"Source: {doc.metadata.get('source', 'N/A')}")
                logger.info(f"Content (first 200 chars): {doc.page_content[:200]}...")
        else:
            logger.warning("No documents found for the query. ChromaDB might be empty or parsing was inaccurate.")

    except Exception as e:
        logger.error(f"Error during ChromaDB verification: {e}", exc_info=True)

if __name__ == "__main__":
    verify_chroma_parsing()
Directory structure:
└── Code-Researcher/
    ├── README.md
    ├── =0.13.0
    ├── =0.21.1
    ├── =4.51.1
    ├── agent_workflow.py
    ├── check_llm_connections.py
    ├── LICENSE
    ├── logging_config.py
    ├── main_api.py
    ├── package.json
    ├── rag.py
    ├── requirements.txt
    ├── setup_rag_pipeline.py
    ├── verify_chroma.py
    ├── chroma_db/
    │   ├── chroma.sqlite3
    │   └── d43ff285-284e-44e6-befa-64f3ce972e8f/
    │       ├── data_level0.bin
    │       ├── header.bin
    │       ├── length.bin
    │       └── link_lists.bin
    ├── frontend/
    │   ├── package.json
    │   ├── tsconfig.json
    │   ├── public/
    │   │   └── index.html
    │   └── src/
    │       ├── App.css
    │       ├── App.tsx
    │       ├── index.css
    │       ├── index.tsx
    │       ├── reportWebVitals.ts
    │       └── types.ts
    ├── frontend-vite/
    │   ├── README.md
    │   ├── eslint.config.js
    │   ├── index.html
    │   ├── package.json
    │   ├── tsconfig.app.json
    │   ├── tsconfig.json
    │   ├── tsconfig.node.json
    │   ├── vite.config.ts
    │   ├── public/
    │   │   └── index.html
    │   └── src/
    │       ├── App.css
    │       ├── App.tsx
    │       ├── index.css
    │       ├── index.tsx
    │       ├── main.tsx
    │       ├── reportWebVitals.ts
    │       ├── types.ts
    │       └── vite-env.d.ts
    ├── tests/
    │   ├── test_agent_workflow.py
    │   └── test_main_api.py
    ├── .roo/
    │   └── mcp.json
    └── .wave/
        └── context_for_wave.txt

================================================
FILE: README.md
================================================
# Code Researcher System

A comprehensive research assistant system combining RAG (Retrieval-Augmented Generation) capabilities with automated agent workflows for code analysis and documentation.

## Features

- **Iterative Research Loop**: Self-correcting workflow that cycles between analysis and research.
- **RAG Pipeline**: Automated document processing and vector storage using ChromaDB.
- **Agent Workflows**: 
  - Lead Researcher (Orchestrates research)
  - Code Analyzer (Searches local codebase)
  - Documentation Searcher (Searches documentation)
  - Web Searcher (Performs web searches)
- **API Integration**: RESTful endpoints for interacting with research modules.
- **Modern Frontend**: React-based UI.

## Architecture Overview

The system uses an iterative research pattern:

```mermaid
graph TD
    A[Lead Researcher] -->|Decision| B{Needs Research?}
    B -->|Yes| C[Tool Router]
    B -->|No| D[Code Generator]
    C -->|Code Analysis| E[Code Analyzer]
    C -->|Docs Search| F[Doc Searcher]
    C -->|Web Search| G[Web Searcher]
    E --> A
    F --> A
    G --> A
    D --> H[Test Generator]
    H --> I[END]
```

Workflow Process

    Lead Researcher receives the user query and forms a plan.

    Determines if additional research is needed.

    Routes to the appropriate tool via the Tool Router to execute the next step in the plan.

    Tool execution results feed back to the Lead Researcher.

    Process repeats until the research plan is complete.

    Final code is generated based on the gathered context, and then tests are created.

Installation

    Clone the repository.

    Install dependencies:
    ```bash
    npm install
    pip install -r requirements.txt
    ```

Set up your environment variables:
```bash
cp .env.example .env
# Edit the .env file with your specific configuration
```

Start the development servers:
```bash
# Frontend (from the /frontend-vite directory)
npm run dev

# Backend
python main_api.py
```

Configuration

Set these environment variables in .env:
```ini
# Example
OLLAMA_HOST_1=*************
OLLAMA_PORT_1=11434
OLLAMA_MODEL_1=okamototk/deepseek-r1:8b
OLLAMA_MODEL_2=okamototk/deepseek-r1:8b
OLLAMA_EMBEDDING_MODEL=all-minilm:l6-v2
CODE_DB_PATH=./chroma_db
```
Usage

Access the system at http://localhost:5173 after starting both servers.


================================================
FILE: =0.13.0
================================================



================================================
FILE: =0.21.1
================================================



================================================
FILE: =4.51.1
================================================
Collecting vllm==0.8.5
  Using cached vllm-0.8.5-cp38-abi3-manylinux1_x86_64.whl.metadata (14 kB)
Requirement already satisfied: gguf in /home/<USER>/miniconda3/lib/python3.11/site-packages (0.10.0)
Requirement already satisfied: tokenizers in /home/<USER>/miniconda3/lib/python3.11/site-packages (0.20.3)
Requirement already satisfied: torch==2.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (2.6.0)
Requirement already satisfied: transformers in /home/<USER>/miniconda3/lib/python3.11/site-packages (4.46.3)
Requirement already satisfied: cachetools in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (5.5.0)
Requirement already satisfied: psutil in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (5.9.5)
Requirement already satisfied: sentencepiece in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.2.0)
Requirement already satisfied: numpy in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.4)
Requirement already satisfied: requests>=2.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (2.32.3)
Requirement already satisfied: tqdm in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.67.1)
Requirement already satisfied: blake3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.0.4)
Requirement already satisfied: py-cpuinfo in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (9.0.0)
Collecting transformers
  Using cached transformers-4.52.4-py3-none-any.whl.metadata (38 kB)
Requirement already satisfied: huggingface-hub>=0.30.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from huggingface-hub[hf_xet]>=0.30.0->vllm==0.8.5) (0.30.2)
Collecting tokenizers
  Using cached tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Requirement already satisfied: protobuf in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.25.7)
Requirement already satisfied: fastapi>=0.115.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (0.115.6)
Requirement already satisfied: aiohttp in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.11.11)
Requirement already satisfied: openai>=1.52.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.58.1)
Requirement already satisfied: pydantic>=2.9 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (2.10.4)
Requirement already satisfied: prometheus_client>=0.18.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.21.1)
Requirement already satisfied: pillow in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (11.0.0)
Requirement already satisfied: prometheus-fastapi-instrumentator>=7.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (7.1.0)
Requirement already satisfied: tiktoken>=0.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.8.0)
Requirement already satisfied: lm-format-enforcer<0.11,>=0.10.11 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.10.11)
Requirement already satisfied: llguidance<0.8.0,>=0.7.9 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.7.19)
Requirement already satisfied: outlines==0.1.11 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.1.11)
Requirement already satisfied: lark==1.2.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.2.2)
Requirement already satisfied: xgrammar==0.1.18 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.1.18)
Requirement already satisfied: typing_extensions>=4.10 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.12.2)
Requirement already satisfied: filelock>=3.16.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.16.1)
Requirement already satisfied: partial-json-parser in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.2.1.1.post5)
Requirement already satisfied: pyzmq>=25.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (26.4.0)
Requirement already satisfied: msgspec in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.19.0)
Collecting gguf
  Using cached gguf-0.17.0-py3-none-any.whl.metadata (4.4 kB)
Requirement already satisfied: importlib_metadata in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (8.0.0)
Requirement already satisfied: mistral_common>=1.5.4 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from mistral_common[opencv]>=1.5.4->vllm==0.8.5) (1.5.4)
Requirement already satisfied: opencv-python-headless>=4.11.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (4.11.0.86)
Requirement already satisfied: pyyaml in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (6.0.2)
Requirement already satisfied: einops in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.6.1)
Requirement already satisfied: compressed-tensors==0.9.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.9.3)
Requirement already satisfied: depyf==0.18.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.18.0)
Requirement already satisfied: cloudpickle in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.1.1)
Requirement already satisfied: watchfiles in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.0.3)
Requirement already satisfied: python-json-logger in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (3.3.0)
Requirement already satisfied: scipy in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.12.0)
Requirement already satisfied: ninja in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (********)
Requirement already satisfied: opentelemetry-sdk<1.27.0,>=1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-api<1.27.0,>=1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-exporter-otlp<1.27.0,>=1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-semantic-conventions-ai<0.5.0,>=0.4.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.4.3)
Requirement already satisfied: numba==0.61.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.61.2)
Requirement already satisfied: ray!=2.44.*,>=2.43.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (2.43.0)
Requirement already satisfied: torchaudio==2.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (2.6.0)
Requirement already satisfied: torchvision==0.21.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.21.0)
Requirement already satisfied: xformers==0.0.29.post2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from vllm==0.8.5) (0.0.29.post2)
Requirement already satisfied: networkx in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (3.1)
Requirement already satisfied: jinja2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (3.1.4)
Requirement already satisfied: fsspec in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (2024.10.0)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (9.1.0.70)
Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.5.8)
Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (11.2.1.3)
Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (10.3.5.147)
Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (11.6.1.9)
Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.3.1.170)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (0.6.2)
Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (2.21.5)
Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (12.4.127)
Requirement already satisfied: triton==3.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (3.2.0)
Requirement already satisfied: sympy==1.13.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from torch==2.6.0) (1.13.1)
Requirement already satisfied: astor in /home/<USER>/miniconda3/lib/python3.11/site-packages (from depyf==0.18.0->vllm==0.8.5) (0.8.1)
Requirement already satisfied: dill in /home/<USER>/miniconda3/lib/python3.11/site-packages (from depyf==0.18.0->vllm==0.8.5) (0.3.6)
Requirement already satisfied: llvmlite<0.45,>=0.44.0dev0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from numba==0.61.2->vllm==0.8.5) (0.44.0)
Requirement already satisfied: interegular in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (0.3.3)
Requirement already satisfied: nest_asyncio in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (1.6.0)
Requirement already satisfied: diskcache in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (5.6.3)
Requirement already satisfied: referencing in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (0.35.1)
Requirement already satisfied: jsonschema in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (4.23.0)
Requirement already satisfied: pycountry in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (23.12.11)
Requirement already satisfied: airportsdata in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (20250224)
Requirement already satisfied: outlines_core==0.1.26 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from outlines==0.1.11->vllm==0.8.5) (0.1.26)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from sympy==1.13.1->torch==2.6.0) (1.3.0)
Requirement already satisfied: packaging in /home/<USER>/miniconda3/lib/python3.11/site-packages (from lm-format-enforcer<0.11,>=0.10.11->vllm==0.8.5) (24.2)
Requirement already satisfied: deprecated>=1.2.6 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-api<1.27.0,>=1.26.0->vllm==0.8.5) (1.2.15)
Requirement already satisfied: zipp>=0.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from importlib_metadata->vllm==0.8.5) (3.21.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-http==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: googleapis-common-protos~=1.52 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.66.0)
Requirement already satisfied: grpcio<2.0.0,>=1.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.68.1)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-proto==1.26.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.26.0->opentelemetry-exporter-otlp<1.27.0,>=1.26.0->vllm==0.8.5) (1.26.0)
Requirement already satisfied: opentelemetry-semantic-conventions==0.47b0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from opentelemetry-sdk<1.27.0,>=1.26.0->vllm==0.8.5) (0.47b0)
Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (3.4.0)
Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (2.2.3)
Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from requests>=2.26.0->vllm==0.8.5) (2024.12.14)
Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from transformers) (2024.11.6)
Requirement already satisfied: safetensors>=0.4.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from transformers) (0.5.3)
Requirement already satisfied: wrapt<2,>=1.10 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from deprecated>=1.2.6->opentelemetry-api<1.27.0,>=1.26.0->vllm==0.8.5) (1.17.0)
Requirement already satisfied: starlette<0.42.0,>=0.40.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.41.3)
Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from pydantic>=2.9->vllm==0.8.5) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from pydantic>=2.9->vllm==0.8.5) (2.27.2)
Requirement already satisfied: anyio<5,>=3.4.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from starlette<0.42.0,>=0.40.0->fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (4.7.0)
Requirement already satisfied: sniffio>=1.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.4.0->starlette<0.42.0,>=0.40.0->fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.3.1)
Requirement already satisfied: fastapi-cli>=0.0.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.0.7)
Requirement already satisfied: httpx>=0.23.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (0.27.2)
Requirement already satisfied: python-multipart>=0.0.7 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (0.0.20)
Requirement already satisfied: email-validator>=2.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi[standard]>=0.115.0->vllm==0.8.5) (2.2.0)
Requirement already satisfied: uvicorn>=0.12.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.34.0)
Requirement already satisfied: dnspython>=2.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from email-validator>=2.0.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (2.7.0)
Requirement already satisfied: typer>=0.12.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.15.1)
Requirement already satisfied: rich-toolkit>=0.11.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.14.3)
Requirement already satisfied: httpcore==1.* in /home/<USER>/miniconda3/lib/python3.11/site-packages (from httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.14.0)
Collecting hf-xet>=0.1.4 (from huggingface-hub[hf_xet]>=0.30.0->vllm==0.8.5)
  Downloading hf_xet-1.1.4-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (879 bytes)
Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jinja2->torch==2.6.0) (3.0.2)
Requirement already satisfied: attrs>=22.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jsonschema->outlines==0.1.11->vllm==0.8.5) (24.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jsonschema->outlines==0.1.11->vllm==0.8.5) (2024.10.1)
Requirement already satisfied: rpds-py>=0.7.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from jsonschema->outlines==0.1.11->vllm==0.8.5) (0.22.3)
Requirement already satisfied: distro<2,>=1.7.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from openai>=1.52.0->vllm==0.8.5) (1.9.0)
Requirement already satisfied: jiter<1,>=0.4.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from openai>=1.52.0->vllm==0.8.5) (0.8.2)
Requirement already satisfied: click>=7.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (8.1.7)
Requirement already satisfied: msgpack<2.0.0,>=1.0.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (1.0.7)
Requirement already satisfied: aiosignal in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (1.3.2)
Requirement already satisfied: frozenlist in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (1.5.0)
Requirement already satisfied: cupy-cuda12x in /home/<USER>/miniconda3/lib/python3.11/site-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (13.4.1)
Requirement already satisfied: rich>=13.7.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (13.9.4)
Requirement already satisfied: markdown-it-py>=2.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from rich>=13.7.1->rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from rich>=13.7.1->rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (2.18.0)
Requirement already satisfied: mdurl~=0.1 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich>=13.7.1->rich-toolkit>=0.11.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.1.2)
Requirement already satisfied: shellingham>=1.3.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from typer>=0.12.3->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.5.4)
Requirement already satisfied: httptools>=0.6.3 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.6.4)
Requirement already satisfied: python-dotenv>=0.13 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (1.0.1)
Requirement already satisfied: uvloop!=0.15.0,!=0.15.1,>=0.14.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (0.21.0)
Requirement already satisfied: websockets>=10.4 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from uvicorn[standard]>=0.12.0; extra == "standard"->fastapi[standard]>=0.115.0->vllm==0.8.5) (14.1)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (2.4.4)
Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (6.1.0)
Requirement already satisfied: propcache>=0.2.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (0.2.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from aiohttp->vllm==0.8.5) (1.18.3)
Requirement already satisfied: fastrlock>=0.5 in /home/<USER>/miniconda3/lib/python3.11/site-packages (from cupy-cuda12x->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.8.5) (0.8.3)
Using cached vllm-0.8.5-cp38-abi3-manylinux1_x86_64.whl (326.4 MB)
Using cached gguf-0.17.0-py3-none-any.whl (95 kB)
Using cached tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)
Using cached transformers-4.52.4-py3-none-any.whl (10.5 MB)
Downloading hf_xet-1.1.4-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.1/3.1 MB 10.0 MB/s eta 0:00:00
Installing collected packages: hf-xet, gguf, tokenizers, transformers, vllm
  Attempting uninstall: gguf
    Found existing installation: gguf 0.10.0
    Uninstalling gguf-0.10.0:
      Successfully uninstalled gguf-0.10.0
  Attempting uninstall: tokenizers
    Found existing installation: tokenizers 0.20.3
    Uninstalling tokenizers-0.20.3:
      Successfully uninstalled tokenizers-0.20.3
  Attempting uninstall: transformers
    Found existing installation: transformers 4.46.3
    Uninstalling transformers-4.46.3:
      Successfully uninstalled transformers-4.46.3

Successfully installed gguf-0.17.0 hf-xet-1.1.4 tokenizers-0.21.1 transformers-4.52.4 vllm-0.8.5



================================================
FILE: agent_workflow.py
================================================
import os
import time
from typing import TypedDict, Annotated, List
from langchain_core.messages import BaseMessage, AnyMessage, HumanMessage, ToolMessage, AIMessage, SystemMessage
from langchain_ollama import ChatOllama
from langgraph.graph import StateGraph, END
from dotenv import load_dotenv
from langchain.tools import tool
from langchain_community.tools import DuckDuckGoSearchRun
from rag import get_retriever
from logging_config import setup_logging
from langchain.globals import set_debug

set_debug(True)
logger = setup_logging()
load_dotenv()

class AgentState(TypedDict):
    user_query: str
    messages: Annotated[List[AnyMessage], lambda x, y: x + y]
    code_analysis_result: str | None
    doc_search_result: str | None
    web_search_result: str | None
    generated_code: str | None
    generated_tests: str | None
    turn_count: int

@tool
def codebase_retriever(query: str) -> str:
    """Retrieves relevant code snippets from the local codebase based on a query."""
    retriever = get_retriever()
    results = retriever.invoke(query)
    return "\n---\n".join([f"File: {doc.metadata.get('source', 'N/A')}\n\n```python\n{doc.page_content}\n```" for doc in results])

@tool
def documentation_retriever(query: str) -> str:
    """Retrieves documentation based on a query."""
    return f"Placeholder documentation for query '{query}': Found API specs and usage examples."

@tool
def web_search(query: str) -> str:
    """
    Performs a web search using DuckDuckGo for external libraries, concepts, or errors.
    """
    logger.info(f"Performing web search for query: '{query}'")
    search = DuckDuckGoSearchRun()
    try:
        result = search.run(query)
        formatted_result = f"Web Search Results for '{query}':\n{result}"
        logger.info(f"Web search for '{query}' returned {len(result)} characters.")
        return formatted_result
    except Exception as e:
        logger.error(f"Web search for '{query}' failed: {e}", exc_info=True)
        return "Web search failed due to an error."

# Map tool names to their functions for dynamic execution
tool_map = {
    "web_search": web_search,
    "codebase_retriever": codebase_retriever,
    "documentation_retriever": documentation_retriever,
}

ollama_client1 = ChatOllama(
    base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
    model=os.getenv("OLLAMA_MODEL_1"),
    temperature=0.1,
)
ollama_client2 = ChatOllama(
    base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
    model=os.getenv("OLLAMA_MODEL_2"),
    temperature=0.1,
)

tools = [codebase_retriever, documentation_retriever, web_search]
lead_researcher_llm = ollama_client2.bind_tools(tools)
code_generator_llm = ollama_client2
test_generator_llm = ollama_client2

def lead_researcher_node(state: AgentState):
    """The lead researcher node that orchestrates the research process."""
    if "turn_count" not in state:
        state["turn_count"] = 0
    state["turn_count"] += 1
    logger.info(f"Lead researcher turn {state['turn_count']}. Invoking LLM...")

    # A more robust system prompt that encourages planning.
    system_prompt = """You are a lead research agent. Your goal is to answer the user's query by orchestrating a team of specialist agents.

1.  **Analyze**: First, analyze the user's query and the conversation history.
2.  **Review**: Second, review the information already gathered in the 'code_analysis_result', 'doc_search_result', and 'web_search_result' fields.
3.  **Plan**: Third, form a step-by-step plan for what new information is needed.
4.  **Execute**: Fourth, decide on the next set of tool calls. Crucially, do NOT call a tool if the required information is already present in the results you reviewed.

The available tools are:
- `codebase_retriever`: Use this to search for code snippets within the local project codebase.
- `documentation_retriever`: Use this to search for official documentation related to the query.
- `web_search`: Use this for general web searches about external libraries, errors, or concepts.

If you have gathered enough information, you can stop calling tools.
Respond *only* with the new, unique tool calls to make. Do not add any other text.
"""
    
    messages_to_send = [SystemMessage(content=system_prompt)] + state['messages']
    # Always remind the agent of the original goal.
    messages_to_send.append(HumanMessage(content=f"My original query was: '{state['user_query']}'. Based on your plan and the conversation so far, what is the next single tool call to make?"))
    
    try:
        response = lead_researcher_llm.invoke(messages_to_send)
        logger.info("Lead researcher LLM invocation complete.")
    except Exception as e:
        logger.error(f"LLM invocation failed: {e}", exc_info=True)
        response = AIMessage(content="LLM_INVOCATION_FAILED")

    return {"messages": [response], "turn_count": state["turn_count"]}

def tool_executor_node(state: AgentState):
    """Executes multiple tool calls in parallel and collects results."""
    logger.info("Entering tool executor node.")
    new_messages = []
    updates = {}
    
    # Ensure tool_calls exist and are not empty
    if not state["messages"][-1].tool_calls:
        logger.warning("No tool calls found in the last message.")
        return {"messages": new_messages}

    tool_calls = state["messages"][-1].tool_calls
    unique_calls = []
    seen = set()
    for call in tool_calls:
        # Create a unique, hashable representation of the call
        # Convert args to a frozenset of (key, value) pairs for hashing
        call_signature = (call['name'], frozenset(call.get('args', {}).items()))
        if call_signature not in seen:
            unique_calls.append(call)
            seen.add(call_signature)

    for tool_call in unique_calls:
        tool_name = tool_call.get("name")
        tool_args = tool_call.get("args", {})
        tool_id = tool_call.get("id")

        if tool_name in tool_map:
            try:
                logger.info(f"Executing unique tool: {tool_name} with args: {tool_args}")
                tool_result = tool_map[tool_name].invoke(tool_args)
                tool_message = ToolMessage(content=tool_result, tool_call_id=tool_id)
                new_messages.append(tool_message)

                # Aggregate results into the state, appending to existing content
                if tool_name == "web_search":
                    if updates.get("web_search_result"):
                        updates["web_search_result"] += f"\n---\n{tool_result}"
                    else:
                        updates["web_search_result"] = tool_result
                elif tool_name == "codebase_retriever":
                    if updates.get("code_analysis_result"):
                        updates["code_analysis_result"] += f"\n---\n{tool_result}"
                    else:
                        updates["code_analysis_result"] = tool_result
                elif tool_name == "documentation_retriever":
                    if updates.get("doc_search_result"):
                        updates["doc_search_result"] += f"\n---\n{tool_result}"
                    else:
                        updates["doc_search_result"] = tool_result
            except Exception as e:
                logger.error(f"Error executing tool {tool_name}: {e}", exc_info=True)
                error_message = ToolMessage(content=f"Error executing {tool_name}: {e}", tool_call_id=tool_id)
                new_messages.append(error_message)
        else:
            logger.warning(f"Unknown tool: {tool_name}")
            error_message = ToolMessage(content=f"Unknown tool: {tool_name}", tool_call_id=tool_id)
            new_messages.append(error_message)
    
    updates["messages"] = new_messages
    return updates

# Add this new node function
def context_summarizer_node(state: AgentState):
    """Summarizes the results of the last tool execution for the planner."""
    logger.info("Entering context summarizer node.")

    # 1. Find the last AI message to determine how many tools were called.
    last_ai_message = None
    for msg in reversed(state['messages']):
        if isinstance(msg, AIMessage) and msg.tool_calls:
            last_ai_message = msg
            break
    
    # If for some reason we can't find a relevant AI message, pass through.
    if not last_ai_message:
        logger.warning("Summarizer could not find an AIMessage with tool_calls to summarize.")
        return {}

    num_tool_calls = len(last_ai_message.tool_calls)
    
    # 2. The tool messages to summarize are the last N messages in the list.
    # These will be the ToolMessage objects from the previous step.
    tool_messages_to_summarize = state['messages'][-num_tool_calls:]
    tool_outputs_for_summary = "\n---\n".join([str(msg.content) for msg in tool_messages_to_summarize])

    if not tool_outputs_for_summary.strip():
        summary_content = "No new information was gathered from the tools."
    else:
        # 3. Summarize the tool outputs using an LLM.
        prompt = f"""You are a helpful assistant. Briefly summarize the following tool results in one or two sentences for a planning agent.

        **Tool Results:**
        {tool_outputs_for_summary[:4000]}

        **Summary:**
        """
        try:
            response = code_generator_llm.invoke([HumanMessage(content=prompt)])
            summary_content = response.content
        except Exception as e:
            logger.error(f"Summarization LLM failed: {e}")
            summary_content = "Failed to summarize tool results."

    # 4. Replace the verbose tool results with a single summary message.
    # The tool_call_id is arbitrary here, as it's just for context.
    summary_message = ToolMessage(content=summary_content, tool_call_id="summary_tool_call")
    
    # Rebuild the history by removing the last N tool messages and adding the summary.
    messages_without_last_tools = state['messages'][:-num_tool_calls]
    messages_without_last_tools.append(summary_message)
    
    return {"messages": messages_without_last_tools}

def code_generator_node(state: AgentState):
    logger.info("Entering code generator node.")
    prompt = f"""
You are an expert programmer tasked with writing a complete, runnable Python script.
Synthesize the information from the context below to fulfill the user's request.
Base the code *only* on the provided context. Do not add functionality not present in the snippets.
Output only the raw Python code, with no commentary or explanation.

**User's Request:**
{state.get('user_query')}

**Relevant Code Snippets Found:**
{state.get('code_analysis_result') or 'No relevant code found.'}

**Relevant Documentation Found:**
{state.get('doc_search_result') or 'No relevant documentation found.'}

**Relevant Web Search Results Found:**
{state.get('web_search_result') or 'No relevant web search results found.'}
"""
    response = code_generator_llm.invoke([HumanMessage(content=prompt)])
    logger.info("Code generator LLM invocation complete.")
    return {"generated_code": response.content}

def test_generator_node(state: AgentState):
    response = test_generator_llm.invoke([HumanMessage(content=f"Generate tests for:\n{state['generated_code']}")])
    return {"generated_tests": response.content}

def route_logic(state: AgentState) -> str:
    # FIRST: Check for a critical failure and end the workflow if found.
    if state["messages"][-1].content == "LLM_INVOCATION_FAILED":
        logger.error("Lead researcher failed to get a response. Terminating workflow.")
        return END

    # If no failure, proceed with the normal logic.
    if state.get("turn_count", 0) > 4:
        logger.warning("Turn count limit reached. Forcing code generation.")
        return "code_generator_node"

    if not state["messages"][-1].tool_calls:
        logger.info("No tool calls detected. Routing to code_generator_node.")
        return "code_generator_node"

    logger.info("Tool calls detected. Routing to tool_executor_node.")
    return "tool_executor_node"

workflow = StateGraph(AgentState)
workflow.add_node("lead_researcher_node", lead_researcher_node)
workflow.add_node("tool_executor_node", tool_executor_node)
workflow.add_node("context_summarizer_node", context_summarizer_node) # Add the new node
workflow.add_node("code_generator_node", code_generator_node)
workflow.add_node("test_generator_node", test_generator_node)

workflow.set_entry_point("lead_researcher_node")

workflow.add_conditional_edges(
    "lead_researcher_node",
    route_logic,
    {
        "tool_executor_node": "tool_executor_node",
        "code_generator_node": "code_generator_node",
        # Add the new explicit end path for failures.
        END: END
    }
)

# Update the edges
workflow.add_edge("tool_executor_node", "context_summarizer_node")
workflow.add_edge("context_summarizer_node", "lead_researcher_node")

workflow.add_edge("code_generator_node", "test_generator_node")
workflow.add_edge("test_generator_node", END)
compiled_app = workflow.compile()



================================================
FILE: check_llm_connections.py
================================================
import os
from dotenv import load_dotenv
from langchain_ollama.llms import OllamaLLM
# from langchain_community.llms import VLLM # Placeholder for VLLM

def main():
    load_dotenv()

    print("Checking Ollama connection...")
    try:
        ollama_host = os.getenv("OLLAMA_HOST_1")
        ollama_port = os.getenv("OLLAMA_PORT_1")
        ollama_model = os.getenv("OLLAMA_MODEL_1")

        if not ollama_host or not ollama_port or not ollama_model:
            print("Ollama host, port, or model not found in .env file. Skipping Ollama check.")
        else:
            # Construct the base URL for Ollama
            ollama_base_url = f"http://{ollama_host}:{ollama_port}"
            print(f"Attempting to connect to Ollama at: {ollama_base_url}")

            # Initialize Ollama LLM client
            # Make sure a model like 'llama2' is pulled in Ollama: `ollama pull llama2`
            llm = OllamaLLM(model=ollama_model, base_url=ollama_base_url)

            # Perform a simple inference
            response = llm.invoke("What is 1+1?")
            print(f"Ollama response: {response}")
            print("Ollama connection successful.")

    except Exception as e:
        print(f"Error connecting to Ollama or during inference: {e}")

    # Placeholder for VLLM connection check
    # print("\nChecking VLLM connection (placeholder)...")
    # try:
    #     vllm_host = os.getenv("VLLM_HOST")
    #     vllm_port = os.getenv("VLLM_PORT")
    #     if not vllm_host or not vllm_port:
    #         print("VLLM host or port not found in .env file. Skipping VLLM check.")
    #     else:
    #         # Construct the endpoint for VLLM
    #         # Note: VLLM uses a different client initialization
    #         # vllm_endpoint = f"http://{vllm_host}:{vllm_port}/v1" # Example, adjust as per VLLM setup
    #         # llm_vllm = VLLM(
    #         #     model="<your_model_name_on_vllm_server>", # Replace with actual model identifier
    #         #     openai_api_key="EMPTY", # Required by VLLM client, but can be empty if no auth
    #         #     openai_api_base=vllm_endpoint,
    #         #     temperature=0,
    #         #     max_tokens=50
    #         # )
    #         # response_vllm = llm_vllm.invoke("Explain relativity in one sentence.")
    #         # print(f"VLLM response: {response_vllm}")
    #         print("VLLM client initialized (actual connection depends on running VLLM server).")
    # except Exception as e:
    #     print(f"Error with VLLM client: {e}")

if __name__ == "__main__":
    main()



================================================
FILE: LICENSE
================================================
                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.



================================================
FILE: logging_config.py
================================================
# logging_config.py
import logging
import sys

def setup_logging(level=logging.INFO):
    # Create a logger
    logger = logging.getLogger('DeepResearchApp')
    logger.setLevel(level)

    # Create a handler (console handler)
    ch = logging.StreamHandler(sys.stdout) # Output to stdout
    ch.setLevel(level)

    # Create a formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s')
    ch.setFormatter(formatter)

    # Add the handler to the logger
    if not logger.handlers: # Avoid adding multiple handlers if called multiple times
        logger.addHandler(ch)

    return logger

# Get a logger instance for immediate use if needed elsewhere
# Or scripts can call setup_logging() themselves.
# logger = setup_logging()



================================================
FILE: main_api.py
================================================
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from agent_workflow import compiled_app
from langchain_core.messages import HumanMessage, BaseMessage
import json
import asyncio
from logging_config import setup_logging

logger = setup_logging()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Add your frontend URLs
    allow_methods=["GET", "POST"],
    allow_headers=["Content-Type"],
)

def _serialize_agent_state(data: dict) -> dict:
    """Helper function to make the AgentState dictionary JSON serializable."""
    if 'messages' in data and isinstance(data.get('messages'), list):
        serializable_messages = []
        for msg in data['messages']:
            if isinstance(msg, BaseMessage):
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_call_names = [call.get('name') for call in msg.tool_calls if call.get('name')]
                    serializable_messages.append(f"Tool Call: {', '.join(tool_call_names)}")
                elif hasattr(msg, 'content'):
                    serializable_messages.append(msg.content)
                else:
                    serializable_messages.append(str(msg))
            else:
                 serializable_messages.append(str(msg))
        data['messages'] = serializable_messages
    return data

async def event_stream(query: str):
    """Streams research events back to the client with proper serialization."""
    logger.info(f"Event stream initiated for query: '{query}'")
    try:
        async for event in compiled_app.astream_events(
            {"user_query": query, "messages": []},
            version="v1",
            config={"recursion_limit": 50}
        ):
            # 1. Initialize payload to None at the start of every loop.
            payload = None
            
            if event["event"] == "on_chain_stream":
                payload = event.get("data", {}).get("chunk")
            elif event["event"] == "on_chain_end":
                payload = event.get("data", {}).get("output")

            # 2. Check that a payload exists before processing it.
            if payload:
                if isinstance(payload, dict):
                    serializable_payload = _serialize_agent_state(payload.copy())
                    yield f"data: {json.dumps(serializable_payload)}\n\n"
                else:
                    yield f"data: {json.dumps({'response': str(payload)})}\n\n"

    except Exception as e:
        logger.error(f"FATAL: An exception occurred during astream_events: {e}", exc_info=True)
        error_message = {"error": "An internal server error occurred during stream processing."}
        yield f"data: {json.dumps(error_message)}\n\n"
    finally:
        logger.info(f"Event stream process fully completed for query: '{query}'")
        yield "data: [DONE]\n\n"

@app.get("/research-stream")
async def stream_research(query: str):
    """Endpoint to initiate and stream the research process via GET."""
    if not query:
        logger.warning("Request received with no query.")
        # We can simply return an empty stream or let the frontend handle it
        return StreamingResponse(iter(["data: [DONE]\n\n"]), media_type="text/event-stream")
    
    return StreamingResponse(event_stream(query), media_type="text/event-stream")

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting FastAPI server...")
    uvicorn.run(app, host="0.0.0.0", port=8003)



================================================
FILE: package.json
================================================
{
  "devDependencies": {
    "@types/react": "^19.1.8",
    "@types/react-dom": "^19.1.6"
  }
}



================================================
FILE: rag.py
================================================
import os
import logging
from dotenv import load_dotenv
from typing import Iterator
from langchain_core.documents import Document
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_community.document_loaders.generic import GenericLoader
from langchain_core.document_loaders.blob_loaders import Blob

from langchain_text_splitters import RecursiveCharacterTextSplitter, Language
import os
import logging
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_core.documents import Document

load_dotenv()

# Setup basic logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# --- FILE TYPE MAPPINGS ---
# Maps file extensions to the Language enum for language-specific splitting
LANGUAGE_MAP = {
    ".py": Language.PYTHON,
    ".js": Language.JS,
    ".ts": Language.TS,
    ".tsx": Language.TS,
    ".java": Language.JAVA,
    ".cpp": Language.CPP,
    ".c": Language.C,
    ".h": Language.CPP,
    ".go": Language.GO,
    ".rb": Language.RUBY,
    ".rs": Language.RUST,
    ".cs": Language.CSHARP,
    ".kt": Language.KOTLIN,
    ".md": Language.MARKDOWN,
}

def ingest_codebase():
    repo_path = "./"
    code_db_path = os.getenv("CODE_DB_PATH")

    if not code_db_path:
        logger.error("CODE_DB_PATH environment variable not set.")
        return

    logger.info(f"Starting codebase ingestion from: {repo_path}")
    all_splits = []
    exclude_dirs = {"venv", ".venv", "node_modules", "__pycache__", "dist", "build", ".git", ".vscode", ".idea", ".mypy_cache", ".DS_Store"}

    # Create a dictionary of splitters
    splitters = {
        lang: RecursiveCharacterTextSplitter.from_language(language=lang, chunk_size=1000, chunk_overlap=200)
        for lang in Language if lang in LANGUAGE_MAP.values()
    }
    # Add a default splitter for languages not in the Language enum
    default_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)

    logger.info("Collecting and parsing files...")
    for root, dirs, files in os.walk(repo_path):
        # Exclude directories from being walked into
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        # Check if the current root directory itself is an excluded directory or a subdirectory of one
        if any(exclude_dir in root.split(os.sep) for exclude_dir in exclude_dirs):
            logger.debug(f"Skipping files in excluded path: {root}")
            continue

        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file_path)[1]
            if file_ext in LANGUAGE_MAP:
                language = LANGUAGE_MAP[file_ext]
                try:
                    with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                        content = f.read()

                    # Select the appropriate splitter
                    splitter = splitters.get(language, default_splitter)
                    docs = splitter.create_documents([content], metadatas=[{"source": file_path, "language": str(language)}])
                    all_splits.extend(docs)
                    logger.info(f"Processed {file_path} with {splitter.__class__.__name__}, created {len(docs)} chunks.")

                except Exception as e:
                    logger.error(f"Could not read or process '{file_path}'. Error: {e}", exc_info=True)
    
    if not all_splits:
        logger.error("No documents collected. Check repo for supported files, exclusion rules, or reading errors.")
        return
        
    logger.info(f"Total chunks to be ingested: {len(all_splits)}")

    # --- ChromaDB Ingestion ---
    try:
        embedding_function = OllamaEmbeddings(
            base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
            model=os.getenv("OLLAMA_EMBEDDING_MODEL")
        )
        logger.info("Creating and persisting vector store in batches...")
        os.makedirs(code_db_path, exist_ok=True)
        
        batch_size = 100
        
        if not all_splits:
            logger.warning("No new documents to ingest after splitting.")
            return

        # Initialize ChromaDB with the first batch
        db = Chroma.from_documents(
            documents=all_splits[:batch_size],
            embedding=embedding_function,
            persist_directory=code_db_path
        )
        # Add remaining documents in batches
        for i in range(batch_size, len(all_splits), batch_size):
            end_index = min(i + batch_size, len(all_splits))
            batch = all_splits[i:end_index]
            if batch:
                logger.info(f"Ingesting batch {i//batch_size + 1}: chunks {i} to {end_index-1}")
                db.add_documents(documents=batch)
        
        logger.info(f"Successfully ingested {len(all_splits)} chunks into ChromaDB at: {code_db_path}")
        
    except Exception as e:
        logger.error(f"Error creating vector store: {e}", exc_info=True)
        return

def get_retriever():
    """Returns a retriever for the ChromaDB."""
    code_db_path = os.getenv("CODE_DB_PATH")
    if not code_db_path:
        raise ValueError("CODE_DB_PATH environment variable not set.")

    embedding_function = OllamaEmbeddings(
        base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
        model=os.getenv("OLLAMA_EMBEDDING_MODEL")
    )
    
    db = Chroma(
        persist_directory=code_db_path,
        embedding_function=embedding_function
    )
    return db.as_retriever()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Ingest a codebase into the RAG pipeline.")
    parser.add_argument("--ingest", action="store_true", help="Run the ingestion process.")
    args = parser.parse_args()
    if args.ingest:
        ingest_codebase()


================================================
FILE: requirements.txt
================================================
python-dotenv
langchain
langchain-ollama
langchain-community
langgraph
fastapi
uvicorn[standard]
chromadb
sentence-transformers
langsmith
pytest
httpx
duckduckgo-search
langchain-text-splitters[all]


================================================
FILE: setup_rag_pipeline.py
================================================
from langchain_ollama import OllamaEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_text_splitters import RecursiveCharacterTextSplitter, Language
from dotenv import load_dotenv
import os

load_dotenv()

# Initialize embeddings with configured model
embeddings = OllamaEmbeddings(
    base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
    model=os.getenv("OLLAMA_EMBEDDING_MODEL")
)

# Configure code-aware text splitter for Python
python_splitter = RecursiveCharacterTextSplitter.from_language(
    language=Language.PYTHON,
    chunk_size=1000,
    chunk_overlap=200
)

# Initialize ChromaDB with configured path
vector_db = Chroma(
    persist_directory=os.getenv("CODE_DB_PATH"),
    embedding_function=embeddings
)

# Existing document processing and DB population logic
# ... [rest of original file content]



================================================
FILE: verify_chroma.py
================================================
import os
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
import logging

load_dotenv()

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def verify_chroma_parsing():
    code_db_path = os.getenv("CODE_DB_PATH")
    if not code_db_path:
        logger.error("CODE_DB_PATH environment variable not set.")
        return

    logger.info(f"Attempting to load ChromaDB from: {code_db_path}")

    try:
        embedding_function = OllamaEmbeddings(
            base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
            model=os.getenv("OLLAMA_EMBEDDING_MODEL")
        )
        
        # Load the existing ChromaDB
        db = Chroma(
            persist_directory=code_db_path,
            embedding_function=embedding_function
        )
        logger.info("ChromaDB loaded successfully.")

        # Perform a similarity search
        query = "rag.py file content"
        logger.info(f"Performing similarity search for query: '{query}'")
        results = db.similarity_search(query, k=5) # Retrieve top 5 results

        if results:
            logger.info(f"Found {len(results)} relevant documents for the query.")
            for i, doc in enumerate(results):
                logger.info(f"--- Document {i+1} ---")
                logger.info(f"Source: {doc.metadata.get('source', 'N/A')}")
                logger.info(f"Content (first 200 chars): {doc.page_content[:200]}...")
        else:
            logger.warning("No documents found for the query. ChromaDB might be empty or parsing was inaccurate.")

    except Exception as e:
        logger.error(f"Error during ChromaDB verification: {e}", exc_info=True)

if __name__ == "__main__":
    verify_chroma_parsing()


================================================
FILE: chroma_db/chroma.sqlite3
================================================
[Non-text file]


================================================
FILE: chroma_db/d43ff285-284e-44e6-befa-64f3ce972e8f/data_level0.bin
================================================
[Non-text file]


================================================
FILE: chroma_db/d43ff285-284e-44e6-befa-64f3ce972e8f/header.bin
================================================
[Non-text file]


================================================
FILE: chroma_db/d43ff285-284e-44e6-befa-64f3ce972e8f/length.bin
================================================
[Non-text file]


================================================
FILE: chroma_db/d43ff285-284e-44e6-befa-64f3ce972e8f/link_lists.bin
================================================



================================================
FILE: frontend/package.json
================================================
{
  "name": "frontend",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "@testing-library/jest-dom": "^5.17.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^13.5.0",
    "@types/jest": "^27.5.2",
    "@types/node": "^16.18.97",
    "axios": "^1.7.2",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-scripts": "5.0.1",
    "typescript": "^4.9.5",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "devDependencies": {
    "@types/react": "^19.1.8",
    "@types/react-dom": "^19.1.6"
  }
}



================================================
FILE: frontend/tsconfig.json
================================================
{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  },
  "include": [
    "src"
  ]
}



================================================
FILE: frontend/public/index.html
================================================
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>



================================================
FILE: frontend/src/App.css
================================================
/* frontend/src/App.css */
.App { text-align: center; font-family: sans-serif; }
.App-header { background-color: #282c34; padding: 20px; color: white; margin-bottom: 20px; }
input[type="text"] { padding: 10px; margin-right: 10px; width: 300px; }
button { padding: 10px; }
.error { color: red; margin-top: 15px; }
.results { margin-top: 20px; text-align: left; padding: 20px; border: 1px solid #eee; }
.results h2, .results h3 { margin-top: 0; }
.results pre { background-color: #f4f4f4; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; }
.results ul { list-style-type: none; padding: 0; }
.results li { background-color: #f9f9f9; border: 1px solid #ddd; padding: 8px; margin-bottom: 5px; border-radius: 3px; }



================================================
FILE: frontend/src/App.tsx
================================================
import React, { useState, FormEvent } from 'react';
import './App.css';

interface AgentState {
  user_query: string;
  active_agent: string;
  code_analysis_result: string | null;
  doc_search_result: string | null;
  web_search_result: string | null;
  generated_code: string | null;
  generated_tests: string | null;
}

const App: React.FC = () => {
  const [query, setQuery] = useState('');
  const [state, setState] = useState<Partial<AgentState> | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!query) return;

    setLoading(true);
    setError('');
    // Clear previous results on new submission
    setState({ user_query: query });
    const es = new EventSource(`http://localhost:8003/research-stream?query=${encodeURIComponent(query)}`);

    es.onmessage = (e) => {
      if (e.data === '[DONE]') {
        es.close();
        setLoading(false);
        return;
      }
      // Correctly merge new data with existing state to preserve all results
      const newData = JSON.parse(e.data);
      setState(prevState => ({ ...prevState, ...newData }));
    };
 
    es.onerror = () => {
      setError('Connection failed');
      es.close();
      setLoading(false);
    };
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Code Researcher System</h1>
        <form onSubmit={handleSubmit}>
          <input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter research query"
            disabled={loading}
          />
          <button type="submit" disabled={loading}>
            {loading ? 'Researching...' : 'Start Research'}
          </button>
        </form>
      </header>

      {state && (
        <div className="results">
          <h2>Results for: {state.user_query}</h2>
          
          {state.code_analysis_result && (
            <div className="section">
              <h3>Codebase Analysis</h3>
              <pre>{state.code_analysis_result}</pre>
            </div>
          )}

          {state.doc_search_result && (
            <div className="section">
              <h3>Documentation Search</h3>
              <pre>{state.doc_search_result}</pre>
            </div>
          )}

          {state.web_search_result && (
            <div className="section">
              <h3>Web Search</h3>
              <pre>{state.web_search_result}</pre>
            </div>
          )}

          {state.generated_code && (
            <div className="section">
              <h3>Generated Code</h3>
              <pre>{state.generated_code}</pre>
            </div>
          )}

          {state.generated_tests && (
            <div className="section">
              <h3>Generated Tests</h3>
              <pre>{state.generated_tests}</pre>
            </div>
          )}
        </div>
      )}

      {error && <div className="error">{error}</div>}
    </div>
  );
};

export default App;



================================================
FILE: frontend/src/index.css
================================================
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}



================================================
FILE: frontend/src/index.tsx
================================================
import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);



================================================
FILE: frontend/src/reportWebVitals.ts
================================================
const reportWebVitals = (onPerfEntry?: () => void) => {
  if (onPerfEntry && typeof onPerfEntry === 'function') {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(onPerfEntry);
      getFID(onPerfEntry);
      getFCP(onPerfEntry);
      getLCP(onPerfEntry);
      getTTFB(onPerfEntry);
    });
  }
};

export default reportWebVitals;


================================================
FILE: frontend/src/types.ts
================================================
export interface ContextMemoryEntry {
  action: string;
  query: string;
  result: string;
  reasoning: string;
}

export interface ResearchStep {
  agent: string;
  timestamp: string;
  content: string;
  contextMemory: ContextMemoryEntry[];
  patch?: string;
}


================================================
FILE: frontend-vite/README.md
================================================
# React + TypeScript + Vite

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and replace with this
    ...tseslint.configs.recommendedTypeChecked,
    // Alternatively, use this for stricter rules
    ...tseslint.configs.strictTypeChecked,
    // Optionally, add this for stylistic rules
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config({
  plugins: {
    // Add the react-x and react-dom plugins
    'react-x': reactX,
    'react-dom': reactDom,
  },
  rules: {
    // other rules...
    // Enable its recommended typescript rules
    ...reactX.configs['recommended-typescript'].rules,
    ...reactDom.configs.recommended.rules,
  },
})
```



================================================
FILE: frontend-vite/eslint.config.js
================================================
import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },
)



================================================
FILE: frontend-vite/index.html
================================================
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>



================================================
FILE: frontend-vite/package.json
================================================
{
  "name": "frontend-vite",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc -b && vite build",
    "lint": "eslint .",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^19.1.0",
    "react-dom": "^19.1.0"
  },
  "devDependencies": {
    "@eslint/js": "^9.25.0",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "@vitejs/plugin-react": "^4.4.1",
    "eslint": "^9.25.0",
    "eslint-plugin-react-hooks": "^5.2.0",
    "eslint-plugin-react-refresh": "^0.4.19",
    "globals": "^16.0.0",
    "typescript": "~5.8.3",
    "typescript-eslint": "^8.30.1",
    "vite": "^6.3.5"
  }
}



================================================
FILE: frontend-vite/tsconfig.app.json
================================================
{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["src"]
}



================================================
FILE: frontend-vite/tsconfig.json
================================================
{
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ]
}



================================================
FILE: frontend-vite/tsconfig.node.json
================================================
{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["vite.config.ts"]
}



================================================
FILE: frontend-vite/vite.config.ts
================================================
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
})



================================================
FILE: frontend-vite/public/index.html
================================================
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>



================================================
FILE: frontend-vite/src/App.css
================================================
/* frontend/src/App.css */
.App { text-align: center; font-family: sans-serif; }
.App-header { background-color: #282c34; padding: 20px; color: white; margin-bottom: 20px; }
input[type="text"] { padding: 10px; margin-right: 10px; width: 300px; }
button { padding: 10px; }
.error { color: red; margin-top: 15px; }
.results { margin-top: 20px; text-align: left; padding: 20px; border: 1px solid #eee; }
.results h2, .results h3 { margin-top: 0; }
.results pre { background-color: #f4f4f4; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; }
.results ul { list-style-type: none; padding: 0; }
.results li { background-color: #f9f9f9; border: 1px solid #ddd; padding: 8px; margin-bottom: 5px; border-radius: 3px; }



================================================
FILE: frontend-vite/src/App.tsx
================================================
import React, { useState, FormEvent } from 'react';
import './App.css';

interface AgentState {
  user_query: string;
  active_agent: string;
  code_analysis_result: string | null;
  doc_search_result: string | null;
  generated_code: string | null;
  generated_tests: string | null;
}

const App: React.FC = () => {
  const [query, setQuery] = useState('');
  const [state, setState] = useState<AgentState | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!query) return;

    setLoading(true);
    setError('');
    const es = new EventSource(`http://localhost:8003/research-stream?query=${encodeURIComponent(query)}`);

    es.onmessage = (e) => {
      if (e.data === '[DONE]') {
        es.close();
        setLoading(false);
        return;
      }
      // Merge new data with existing state to preserve all results
      const newData = JSON.parse(e.data);
      setState(prevState => ({...prevState, ...newData}));
    };
 
    es.onerror = () => {
      setError('Connection failed');
      es.close();
      setLoading(false);
    };
  };

  return (
    <div className="App">
      <header>
        <h1>Deep Research System</h1>
        <form onSubmit={handleSubmit}>
          <input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter research query"
            disabled={loading}
          />
          <button disabled={loading}>
            {loading ? 'Researching...' : 'Start'}
          </button>
        </form>
      </header>

      {state && (
        <div className="results">
          <h2>Results for: {state.user_query}</h2>
          <p>Active Agent: {state.active_agent}</p>
          
          {state.code_analysis_result && (
            <div className="section">
              <h3>Code Analysis</h3>
              <pre>{state.code_analysis_result}</pre>
            </div>
          )}

          {state.doc_search_result && (
            <div className="section">
              <h3>Documentation</h3>
              <pre>{state.doc_search_result}</pre>
            </div>
          )}

          {state.generated_code && (
            <div className="section">
              <h3>Generated Code</h3>
              <pre>{state.generated_code}</pre>
            </div>
          )}

          {state.generated_tests && (
            <div className="section">
              <h3>Generated Tests</h3>
              <pre>{state.generated_tests}</pre>
            </div>
          )}
        </div>
      )}

      {error && <div className="error">{error}</div>}
    </div>
  );
};

export default App;



================================================
FILE: frontend-vite/src/index.css
================================================
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}



================================================
FILE: frontend-vite/src/index.tsx
================================================
import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);



================================================
FILE: frontend-vite/src/main.tsx
================================================
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)



================================================
FILE: frontend-vite/src/reportWebVitals.ts
================================================
const reportWebVitals = (onPerfEntry?: () => void) => {
  if (onPerfEntry && typeof onPerfEntry === 'function') {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(onPerfEntry);
      getFID(onPerfEntry);
      getFCP(onPerfEntry);
      getLCP(onPerfEntry);
      getTTFB(onPerfEntry);
    });
  }
};

export default reportWebVitals;


================================================
FILE: frontend-vite/src/types.ts
================================================
export interface ContextMemoryEntry {
  action: string;
  query: string;
  result: string;
  reasoning: string;
}

export interface ResearchStep {
  agent: string;
  timestamp: string;
  content: string;
  contextMemory: ContextMemoryEntry[];
  patch?: string;
}


================================================
FILE: frontend-vite/src/vite-env.d.ts
================================================
/// <reference types="vite/client" />



================================================
FILE: tests/test_agent_workflow.py
================================================
import pytest
from unittest.mock import MagicMock, patch
from agent_workflow import AgentState, lead_researcher_node, code_analyzer_node, doc_searcher_node, web_search_node, code_generator_node, route_logic, compiled_app
from langchain_core.messages import AIMessage, ToolCall

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio

@pytest.fixture
def initial_state() -> AgentState:
    """Provides a clean initial state for each test."""
    return {
        "user_query": "How do I use the web_search tool?",
        "messages": [],
        "turn_count": 0,
        "code_analysis_result": None,
        "doc_search_result": None,
        "web_search_result": None,
        "generated_code": None,
        "generated_tests": None,
    }

@patch('agent_workflow.lead_researcher_llm')
async def test_lead_researcher_calls_tool(mock_llm, initial_state):
    """Test that the lead researcher correctly invokes a tool based on the user query."""
    # Arrange: Configure the mock LLM to return a specific tool call
    tool_call = ToolCall(name="web_search", args={"query": "how to use web_search tool"}, id="123")
    mock_llm.invoke.return_value = AIMessage(content="", tool_calls=[tool_call])
    
    # Act
    result_state = lead_researcher_node(initial_state)
    
    # Assert
    assert len(result_state["messages"]) == 1
    final_message = result_state["messages"][0]
    assert isinstance(final_message, AIMessage)
    assert final_message.tool_calls[0].get("name") == "web_search"
    # Verify the prompt contained the original query
    mock_llm.invoke.assert_called_once()
    assert "My original query was: 'How do I use the web_search tool?'" in str(mock_llm.invoke.call_args)


def test_route_logic_chooses_correct_node(initial_state):
    """Test that the router sends the workflow to the correct node based on the tool call."""
    # Arrange
    tool_call = ToolCall(name="web_search", args={"query": "testing"}, id="123")
    initial_state["messages"].append(AIMessage(content="", tool_calls=[tool_call]))
    
    # Act
    decision = route_logic(initial_state)
    
    # Assert
    assert decision == "web_search_node"

def test_route_logic_goes_to_generator_if_no_tool_call(initial_state):
    """Test that the router proceeds to code generation if no tool is called."""
    # Arrange
    initial_state["messages"].append(AIMessage(content="I have enough info."))
    
    # Act
    decision = route_logic(initial_state)
    
    # Assert
    assert decision == "code_generator_node"

@patch('agent_workflow.web_search')
def test_web_search_node_updates_state(mock_web_search, initial_state):
    """Verify the web_search_node calls its tool and updates the state correctly."""
    # Arrange
    tool_call = ToolCall(name="web_search", args={"query": "test query"}, id="123")
    initial_state["messages"].append(AIMessage(content="", tool_calls=[tool_call]))
    mock_web_search.invoke.return_value = "Search results here."
    
    # Act
    result_state = web_search_node(initial_state)
    
    # Assert
    mock_web_search.invoke.assert_called_once_with({'query': 'test query'})
    assert result_state["web_search_result"] == "Search results here."
    assert result_state["messages"][-1].content == "Search results here."

@patch('agent_workflow.code_generator_llm')
def test_code_generator_node(mock_llm, initial_state):
    """Test that the code generator receives the correct, synthesized prompt."""
    # Arrange
    initial_state["user_query"] = "Make a hello world app."
    initial_state["web_search_result"] = "print('hello world')"
    mock_llm.invoke.return_value = AIMessage(content="```python\nprint('hello world')\n```")
    
    # Act
    result_state = code_generator_node(initial_state)
    
    # Assert
    assert result_state["generated_code"] is not None
    # Check that the prompt sent to the LLM contains the context
    prompt_sent = mock_llm.invoke.call_args[0][0][0].content
    assert "User's Request:\nMake a hello world app." in prompt_sent
    assert "Relevant Web Search Results Found:\nprint('hello world')" in prompt_sent
    assert "Relevant Documentation Found:\nNo relevant documentation found." in prompt_sent


================================================
FILE: tests/test_main_api.py
================================================
import pytest
from httpx import AsyncClient
import json
from unittest.mock import patch, MagicMock

# Adjust path to import from the root directory
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from main_api import app

pytestmark = pytest.mark.asyncio

@pytest.fixture
async def client():
    """Provides an async client for testing the FastAPI app."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

async def test_research_stream_endpoint_empty_query(client: AsyncClient):
    """Tests that the stream endpoint handles an empty query gracefully."""
    response = await client.get("/research-stream?query=")
    assert response.status_code == 200
    assert response.headers['content-type'] == 'text/event-stream; charset=utf-8'
    
    content = await response.aread()
    assert content.decode('utf-8').strip() == "data: [DONE]"

@patch('main_api.compiled_app')
async def test_research_stream_success(mock_compiled_app: MagicMock, client: AsyncClient):
    """
    Tests a successful research stream by mocking the agent workflow.
    This independently verifies the API layer's streaming and serialization logic.
    """
    query = "test query"
    
    # Define the mock stream of events that the workflow would produce.
    # We only care about the final 'on_chain_end' event for this test.
    final_state = {
        "user_query": query,
        "messages": [
            "Tool Call: web_search" 
        ],
        "web_search_result": "Mocked web search result.",
        "generated_code": "print('hello world')",
        "generated_tests": "assert True"
    }
    mock_events = [
        {
            "event": "on_chain_end",
            "data": {"output": final_state}
        }
    ]

    # Create an async generator for the mock stream
    async def mock_event_stream(*args, **kwargs):
        for event in mock_events:
            yield event

    mock_compiled_app.astream_events.return_value = mock_event_stream()

    response = await client.get(f"/research-stream?query={query}")
    
    assert response.status_code == 200
    
    # Process the streamed response
    content = await response.aread()
    lines = content.decode('utf-8').strip().split('\n\n')
    
    assert len(lines) == 2
    assert lines[0].startswith("data:")
    assert lines[1] == "data: [DONE]"
    
    # Verify the content of the main data event
    data = json.loads(lines[0].replace("data: ", ""))
    assert data['user_query'] == query
    assert data['generated_code'] == "print('hello world')"
    assert data['web_search_result'] == "Mocked web search result."

    # Verify that the mock was called correctly
    mock_compiled_app.astream_events.assert_called_once_with(
        {"user_query": query, "messages": []},
        version="v1",
        config={"recursion_limit": 50}
    )



================================================
FILE: .roo/mcp.json
================================================



================================================
FILE: .wave/context_for_wave.txt
================================================



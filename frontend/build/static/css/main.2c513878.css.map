{"version": 3, "file": "static/css/main.2c513878.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCXA,KAA2B,sBAAuB,CAA3C,iBAA6C,CACpD,YAAc,wBAAyB,CAAiB,UAAY,CAAE,kBAAmB,CAAhD,YAAkD,CAC3F,iBAAoC,iBAAkB,CAAjC,YAAa,CAAsB,WAAc,CACtE,OAAS,YAAe,CACxB,OAAS,SAAU,CAAE,eAAkB,CACvC,SAA8D,qBAAsB,CAAzE,eAAgB,CAAoB,YAAa,CAA/B,eAAyD,CACtF,wBAA2B,YAAe,CAC1C,aAAoG,oBAAqB,CAA1G,wBAAyB,CAAiB,iBAAkB,CAAjC,YAAa,CAAsB,oBAA8C,CAC3H,YAAc,oBAAqB,CAAE,SAAY,CACjD,YAAc,wBAAyB,CAAE,qBAAsB,CAAoC,iBAAkB,CAAtC,iBAAkB,CAAhC,WAAsD", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "/* frontend/src/App.css */\n.App { text-align: center; font-family: sans-serif; }\n.App-header { background-color: #282c34; padding: 20px; color: white; margin-bottom: 20px; }\ninput[type=\"text\"] { padding: 10px; margin-right: 10px; width: 300px; }\nbutton { padding: 10px; }\n.error { color: red; margin-top: 15px; }\n.results { margin-top: 20px; text-align: left; padding: 20px; border: 1px solid #eee; }\n.results h2, .results h3 { margin-top: 0; }\n.results pre { background-color: #f4f4f4; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; }\n.results ul { list-style-type: none; padding: 0; }\n.results li { background-color: #f9f9f9; border: 1px solid #ddd; padding: 8px; margin-bottom: 5px; border-radius: 3px; }\n"], "names": [], "sourceRoot": ""}
import React, { useState, FormEvent } from 'react';
import './App.css';

interface AgentState {
  user_query: string;
  active_agent: string;
  code_analysis_result: string | null;
  doc_search_result: string | null;
  web_search_result: string | null;
  generated_code: string | null;
  generated_tests: string | null;
}

const App: React.FC = () => {
  const [query, setQuery] = useState('');
  const [state, setState] = useState<Partial<AgentState> | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!query) return;

    setLoading(true);
    setError('');
    // Clear previous results on new submission
    setState({ user_query: query });
    const es = new EventSource(`http://localhost:8003/research-stream?query=${encodeURIComponent(query)}`);

    es.onmessage = (e) => {
      if (e.data === '[DONE]') {
        es.close();
        setLoading(false);
        return;
      }
      // Correctly merge new data with existing state to preserve all results
      const newData = JSON.parse(e.data);
      setState(prevState => ({ ...prevState, ...newData }));
    };
 
    es.onerror = () => {
      setError('Connection failed');
      es.close();
      setLoading(false);
    };
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Code Researcher System</h1>
        <form onSubmit={handleSubmit}>
          <input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter research query"
            disabled={loading}
          />
          <button type="submit" disabled={loading}>
            {loading ? 'Researching...' : 'Start Research'}
          </button>
        </form>
      </header>

      {state && (
        <div className="results">
          <h2>Results for: {state.user_query}</h2>
          
          {state.code_analysis_result && (
            <div className="section">
              <h3>Codebase Analysis</h3>
              <pre>{state.code_analysis_result}</pre>
            </div>
          )}

          {state.doc_search_result && (
            <div className="section">
              <h3>Documentation Search</h3>
              <pre>{state.doc_search_result}</pre>
            </div>
          )}

          {state.web_search_result && (
            <div className="section">
              <h3>Web Search</h3>
              <pre>{state.web_search_result}</pre>
            </div>
          )}

          {state.generated_code && (
            <div className="section">
              <h3>Generated Code</h3>
              <pre>{state.generated_code}</pre>
            </div>
          )}

          {state.generated_tests && (
            <div className="section">
              <h3>Generated Tests</h3>
              <pre>{state.generated_tests}</pre>
            </div>
          )}
        </div>
      )}

      {error && <div className="error">{error}</div>}
    </div>
  );
};

export default App;

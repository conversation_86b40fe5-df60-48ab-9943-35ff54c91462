import os
from dotenv import load_dotenv
from langchain_ollama.llms import OllamaLLM
# from langchain_community.llms import VLLM # Placeholder for VLLM

def main():
    load_dotenv()

    print("Checking Ollama connection...")
    try:
        ollama_host = os.getenv("OLLAMA_HOST_1")
        ollama_port = os.getenv("OLLAMA_PORT_1")
        ollama_model = os.getenv("OLLAMA_MODEL_1")

        if not ollama_host or not ollama_port or not ollama_model:
            print("Ollama host, port, or model not found in .env file. Skipping Ollama check.")
        else:
            # Construct the base URL for Ollama
            ollama_base_url = f"http://{ollama_host}:{ollama_port}"
            print(f"Attempting to connect to Ollama at: {ollama_base_url}")

            # Initialize Ollama LLM client
            # Make sure a model like 'llama2' is pulled in Ollama: `ollama pull llama2`
            llm = OllamaLLM(model=ollama_model, base_url=ollama_base_url)

            # Perform a simple inference
            response = llm.invoke("What is 1+1?")
            print(f"Ollama response: {response}")
            print("Ollama connection successful.")

    except Exception as e:
        print(f"Error connecting to Ollama or during inference: {e}")

    # Placeholder for VLLM connection check
    # print("\nChecking VLLM connection (placeholder)...")
    # try:
    #     vllm_host = os.getenv("VLLM_HOST")
    #     vllm_port = os.getenv("VLLM_PORT")
    #     if not vllm_host or not vllm_port:
    #         print("VLLM host or port not found in .env file. Skipping VLLM check.")
    #     else:
    #         # Construct the endpoint for VLLM
    #         # Note: VLLM uses a different client initialization
    #         # vllm_endpoint = f"http://{vllm_host}:{vllm_port}/v1" # Example, adjust as per VLLM setup
    #         # llm_vllm = VLLM(
    #         #     model="<your_model_name_on_vllm_server>", # Replace with actual model identifier
    #         #     openai_api_key="EMPTY", # Required by VLLM client, but can be empty if no auth
    #         #     openai_api_base=vllm_endpoint,
    #         #     temperature=0,
    #         #     max_tokens=50
    #         # )
    #         # response_vllm = llm_vllm.invoke("Explain relativity in one sentence.")
    #         # print(f"VLLM response: {response_vllm}")
    #         print("VLLM client initialized (actual connection depends on running VLLM server).")
    # except Exception as e:
    #     print(f"Error with VLLM client: {e}")

if __name__ == "__main__":
    main()

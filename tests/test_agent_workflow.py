import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from agent_workflow import <PERSON><PERSON><PERSON>, lead_researcher_node, code_analyzer_node, doc_searcher_node, web_search_node, code_generator_node, route_logic, compiled_app
from langchain_core.messages import AIMessage, ToolCall

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio

@pytest.fixture
def initial_state() -> AgentState:
    """Provides a clean initial state for each test."""
    return {
        "user_query": "How do I use the web_search tool?",
        "messages": [],
        "turn_count": 0,
        "code_analysis_result": None,
        "doc_search_result": None,
        "web_search_result": None,
        "generated_code": None,
        "generated_tests": None,
    }

@patch('agent_workflow.lead_researcher_llm')
async def test_lead_researcher_calls_tool(mock_llm, initial_state):
    """Test that the lead researcher correctly invokes a tool based on the user query."""
    # Arrange: Configure the mock LLM to return a specific tool call
    tool_call = ToolCall(name="web_search", args={"query": "how to use web_search tool"}, id="123")
    mock_llm.invoke.return_value = AIMessage(content="", tool_calls=[tool_call])
    
    # Act
    result_state = lead_researcher_node(initial_state)
    
    # Assert
    assert len(result_state["messages"]) == 1
    final_message = result_state["messages"][0]
    assert isinstance(final_message, AIMessage)
    assert final_message.tool_calls[0].get("name") == "web_search"
    # Verify the prompt contained the original query
    mock_llm.invoke.assert_called_once()
    assert "My original query was: 'How do I use the web_search tool?'" in str(mock_llm.invoke.call_args)


def test_route_logic_chooses_correct_node(initial_state):
    """Test that the router sends the workflow to the correct node based on the tool call."""
    # Arrange
    tool_call = ToolCall(name="web_search", args={"query": "testing"}, id="123")
    initial_state["messages"].append(AIMessage(content="", tool_calls=[tool_call]))
    
    # Act
    decision = route_logic(initial_state)
    
    # Assert
    assert decision == "web_search_node"

def test_route_logic_goes_to_generator_if_no_tool_call(initial_state):
    """Test that the router proceeds to code generation if no tool is called."""
    # Arrange
    initial_state["messages"].append(AIMessage(content="I have enough info."))
    
    # Act
    decision = route_logic(initial_state)
    
    # Assert
    assert decision == "code_generator_node"

@patch('agent_workflow.web_search')
def test_web_search_node_updates_state(mock_web_search, initial_state):
    """Verify the web_search_node calls its tool and updates the state correctly."""
    # Arrange
    tool_call = ToolCall(name="web_search", args={"query": "test query"}, id="123")
    initial_state["messages"].append(AIMessage(content="", tool_calls=[tool_call]))
    mock_web_search.invoke.return_value = "Search results here."
    
    # Act
    result_state = web_search_node(initial_state)
    
    # Assert
    mock_web_search.invoke.assert_called_once_with({'query': 'test query'})
    assert result_state["web_search_result"] == "Search results here."
    assert result_state["messages"][-1].content == "Search results here."

@patch('agent_workflow.code_generator_llm')
def test_code_generator_node(mock_llm, initial_state):
    """Test that the code generator receives the correct, synthesized prompt."""
    # Arrange
    initial_state["user_query"] = "Make a hello world app."
    initial_state["web_search_result"] = "print('hello world')"
    mock_llm.invoke.return_value = AIMessage(content="```python\nprint('hello world')\n```")
    
    # Act
    result_state = code_generator_node(initial_state)
    
    # Assert
    assert result_state["generated_code"] is not None
    # Check that the prompt sent to the LLM contains the context
    prompt_sent = mock_llm.invoke.call_args[0][0][0].content
    assert "User's Request:\nMake a hello world app." in prompt_sent
    assert "Relevant Web Search Results Found:\nprint('hello world')" in prompt_sent
    assert "Relevant Documentation Found:\nNo relevant documentation found." in prompt_sent
import pytest
from httpx import AsyncClient
import json
from unittest.mock import patch, MagicMock

# Adjust path to import from the root directory
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from main_api import app

pytestmark = pytest.mark.asyncio

@pytest.fixture
async def client():
    """Provides an async client for testing the FastAPI app."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

async def test_research_stream_endpoint_empty_query(client: AsyncClient):
    """Tests that the stream endpoint handles an empty query gracefully."""
    response = await client.get("/research-stream?query=")
    assert response.status_code == 200
    assert response.headers['content-type'] == 'text/event-stream; charset=utf-8'
    
    content = await response.aread()
    assert content.decode('utf-8').strip() == "data: [DONE]"

@patch('main_api.compiled_app')
async def test_research_stream_success(mock_compiled_app: MagicMock, client: AsyncClient):
    """
    Tests a successful research stream by mocking the agent workflow.
    This independently verifies the API layer's streaming and serialization logic.
    """
    query = "test query"
    
    # Define the mock stream of events that the workflow would produce.
    # We only care about the final 'on_chain_end' event for this test.
    final_state = {
        "user_query": query,
        "messages": [
            "Tool Call: web_search" 
        ],
        "web_search_result": "Mocked web search result.",
        "generated_code": "print('hello world')",
        "generated_tests": "assert True"
    }
    mock_events = [
        {
            "event": "on_chain_end",
            "data": {"output": final_state}
        }
    ]

    # Create an async generator for the mock stream
    async def mock_event_stream(*args, **kwargs):
        for event in mock_events:
            yield event

    mock_compiled_app.astream_events.return_value = mock_event_stream()

    response = await client.get(f"/research-stream?query={query}")
    
    assert response.status_code == 200
    
    # Process the streamed response
    content = await response.aread()
    lines = content.decode('utf-8').strip().split('\n\n')
    
    assert len(lines) == 2
    assert lines[0].startswith("data:")
    assert lines[1] == "data: [DONE]"
    
    # Verify the content of the main data event
    data = json.loads(lines[0].replace("data: ", ""))
    assert data['user_query'] == query
    assert data['generated_code'] == "print('hello world')"
    assert data['web_search_result'] == "Mocked web search result."

    # Verify that the mock was called correctly
    mock_compiled_app.astream_events.assert_called_once_with(
        {"user_query": query, "messages": []},
        version="v1",
        config={"recursion_limit": 50}
    )

/* frontend/src/App.css */
.App { text-align: center; font-family: sans-serif; }
.App-header { background-color: #282c34; padding: 20px; color: white; margin-bottom: 20px; }
input[type="text"] { padding: 10px; margin-right: 10px; width: 300px; }
button { padding: 10px; }
.error { color: red; margin-top: 15px; }
.results { margin-top: 20px; text-align: left; padding: 20px; border: 1px solid #eee; }
.results h2, .results h3 { margin-top: 0; }
.results pre { background-color: #f4f4f4; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; }
.results ul { list-style-type: none; padding: 0; }
.results li { background-color: #f9f9f9; border: 1px solid #ddd; padding: 8px; margin-bottom: 5px; border-radius: 3px; }

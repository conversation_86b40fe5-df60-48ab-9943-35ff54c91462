import os
import logging
from dotenv import load_dotenv
from typing import Iterator
from langchain_core.documents import Document
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_community.document_loaders.generic import GenericLoader
from langchain_core.document_loaders.blob_loaders import Blob

from langchain_text_splitters import RecursiveCharacterTextSplitter, Language
import os
import logging
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_core.documents import Document

load_dotenv()

# Setup basic logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# --- FILE TYPE MAPPINGS ---
# Maps file extensions to the Language enum for language-specific splitting
LANGUAGE_MAP = {
    ".py": Language.PYTHON,
    ".js": Language.JS,
    ".ts": Language.TS,
    ".tsx": Language.TS,
    ".java": Language.JAVA,
    ".cpp": Language.CPP,
    ".c": Language.C,
    ".h": Language.CPP,
    ".go": Language.GO,
    ".rb": Language.RUBY,
    ".rs": Language.RUST,
    ".cs": Language.CSHARP,
    ".kt": Language.KOTLIN,
    ".md": Language.MARKDOWN,
}

def ingest_codebase():
    repo_path = "./"
    code_db_path = os.getenv("CODE_DB_PATH")

    if not code_db_path:
        logger.error("CODE_DB_PATH environment variable not set.")
        return

    logger.info(f"Starting codebase ingestion from: {repo_path}")
    all_splits = []
    exclude_dirs = {"venv", ".venv", "node_modules", "__pycache__", "dist", "build", ".git", ".vscode", ".idea", ".mypy_cache", ".DS_Store"}

    # Create a dictionary of splitters
    splitters = {
        lang: RecursiveCharacterTextSplitter.from_language(language=lang, chunk_size=1000, chunk_overlap=200)
        for lang in Language if lang in LANGUAGE_MAP.values()
    }
    # Add a default splitter for languages not in the Language enum
    default_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)

    logger.info("Collecting and parsing files...")
    for root, dirs, files in os.walk(repo_path):
        # Exclude directories from being walked into
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        # Check if the current root directory itself is an excluded directory or a subdirectory of one
        if any(exclude_dir in root.split(os.sep) for exclude_dir in exclude_dirs):
            logger.debug(f"Skipping files in excluded path: {root}")
            continue

        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file_path)[1]
            if file_ext in LANGUAGE_MAP:
                language = LANGUAGE_MAP[file_ext]
                try:
                    with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                        content = f.read()

                    # Select the appropriate splitter
                    splitter = splitters.get(language, default_splitter)
                    docs = splitter.create_documents([content], metadatas=[{"source": file_path, "language": str(language)}])
                    all_splits.extend(docs)
                    logger.info(f"Processed {file_path} with {splitter.__class__.__name__}, created {len(docs)} chunks.")

                except Exception as e:
                    logger.error(f"Could not read or process '{file_path}'. Error: {e}", exc_info=True)
    
    if not all_splits:
        logger.error("No documents collected. Check repo for supported files, exclusion rules, or reading errors.")
        return
        
    logger.info(f"Total chunks to be ingested: {len(all_splits)}")

    # --- ChromaDB Ingestion ---
    try:
        embedding_function = OllamaEmbeddings(
            base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
            model=os.getenv("OLLAMA_EMBEDDING_MODEL")
        )
        logger.info("Creating and persisting vector store in batches...")
        os.makedirs(code_db_path, exist_ok=True)
        
        batch_size = 100
        
        if not all_splits:
            logger.warning("No new documents to ingest after splitting.")
            return

        # Initialize ChromaDB with the first batch
        db = Chroma.from_documents(
            documents=all_splits[:batch_size],
            embedding=embedding_function,
            persist_directory=code_db_path
        )
        # Add remaining documents in batches
        for i in range(batch_size, len(all_splits), batch_size):
            end_index = min(i + batch_size, len(all_splits))
            batch = all_splits[i:end_index]
            if batch:
                logger.info(f"Ingesting batch {i//batch_size + 1}: chunks {i} to {end_index-1}")
                db.add_documents(documents=batch)
        
        logger.info(f"Successfully ingested {len(all_splits)} chunks into ChromaDB at: {code_db_path}")
        
    except Exception as e:
        logger.error(f"Error creating vector store: {e}", exc_info=True)
        return

def get_retriever():
    """Returns a retriever for the ChromaDB."""
    code_db_path = os.getenv("CODE_DB_PATH")
    if not code_db_path:
        raise ValueError("CODE_DB_PATH environment variable not set.")

    embedding_function = OllamaEmbeddings(
        base_url=f"http://{os.getenv('OLLAMA_HOST_1')}:{os.getenv('OLLAMA_PORT_1')}",
        model=os.getenv("OLLAMA_EMBEDDING_MODEL")
    )
    
    db = Chroma(
        persist_directory=code_db_path,
        embedding_function=embedding_function
    )
    return db.as_retriever()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Ingest a codebase into the RAG pipeline.")
    parser.add_argument("--ingest", action="store_true", help="Run the ingestion process.")
    args = parser.parse_args()
    if args.ingest:
        ingest_codebase()